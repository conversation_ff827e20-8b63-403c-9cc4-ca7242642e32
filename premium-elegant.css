/* ======================================== */
/* PREMIUM ELEGANT DESIGN - Супер утонченный дизайн уровня премиум студий */
/* ======================================== */

/* --- ПРЕМИУМ ЦВЕТОВАЯ ПАЛИТРА --- */
:root {
  /* Элегантные монохромные цвета */
  --premium-primary: #ffffff;
  --premium-secondary: #f8f9fa;
  --premium-accent: #6c757d;
  --premium-dark: #212529;
  --premium-darker: #1a1d20;
  --premium-light: #e9ecef;
  --premium-border: rgba(255, 255, 255, 0.1);
  
  /* Тонкие акценты */
  --premium-blue: #4a90e2;
  --premium-success: #28a745;
  --premium-warning: #ffc107;
  --premium-danger: #dc3545;
  
  /* Градиенты */
  --premium-gradient: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  --premium-gradient-hover: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
  --premium-gradient-active: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  
  /* Тени */
  --premium-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --premium-shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --premium-shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);
  --premium-shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.25);
  
  /* Размытие */
  --premium-blur: blur(20px);
  --premium-blur-strong: blur(40px);
}

/* --- ПРЕМИУМ АНИМАЦИИ --- */
@keyframes premium-fade-in {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes premium-scale-in {
  from { 
    opacity: 0; 
    transform: scale(0.95); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}

@keyframes premium-glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
  }
  50% { 
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.2);
  }
}

@keyframes premium-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-2px); }
}

@keyframes premium-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* --- ОСНОВНОЙ КОНТЕЙНЕР --- */
.app-container {
  background: linear-gradient(135deg, #1a1d20 0%, #2d3436 50%, #1a1d20 100%);
  background-attachment: fixed;
  position: relative;
  overflow: hidden;
}

/* Элегантная текстура */
.app-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.01) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Тонкие линии */
.app-container::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 100px 100px;
  pointer-events: none;
  z-index: -1;
  opacity: 0.5;
}

/* --- ПРЕМИУМ ШАПКА --- */
.app-header {
  background: rgba(26, 29, 32, 0.95);
  backdrop-filter: var(--premium-blur);
  border-bottom: 1px solid var(--premium-border);
  box-shadow: var(--premium-shadow-md);
  position: relative;
}

.app-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

/* Пользовательская информация */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--premium-gradient);
  border: 1px solid var(--premium-border);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-avatar:hover {
  background: var(--premium-gradient-hover);
  transform: scale(1.05);
  box-shadow: var(--premium-shadow-md);
}

.user-name {
  color: var(--premium-primary);
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.5px;
}

/* Информация о балансе */
.balance-info {
  background: var(--premium-gradient);
  border: 1px solid var(--premium-border);
  border-radius: 20px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.balance-info:hover {
  background: var(--premium-gradient-hover);
  transform: translateY(-1px);
  box-shadow: var(--premium-shadow-md);
}

.balance-amount {
  color: var(--premium-primary);
  font-weight: 600;
  font-size: 16px;
}

.balance-currency {
  color: var(--premium-accent);
  font-size: 14px;
}

/* --- ПРЕМИУМ КНОПКИ --- */
.action-button {
  position: relative;
  background: var(--premium-gradient);
  border: 1px solid var(--premium-border);
  border-radius: 16px;
  color: var(--premium-primary);
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.5px;
  padding: 16px 24px;
  margin: 8px 0;
  width: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  backdrop-filter: var(--premium-blur);
  cursor: pointer;
  outline: none;
}

/* Элегантный эффект мерцания */
.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-button:hover::before {
  left: 100%;
}

.action-button:hover {
  background: var(--premium-gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--premium-shadow-lg);
  border-color: rgba(255, 255, 255, 0.2);
}

.action-button:active {
  transform: translateY(0);
  background: var(--premium-gradient-active);
  box-shadow: var(--premium-shadow-md);
}

/* Убираем цветные классы - делаем все кнопки одинаковыми */
.purple-button,
.blue-button,
.orange-button {
  /* Наследуем стили от .action-button */
}

/* Специальные состояния */
.action-button:disabled {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.05);
  color: var(--premium-accent);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.action-button:disabled::before {
  display: none;
}

/* --- ПРЕМИУМ НАВИГАЦИЯ --- */
.app-nav {
  background: rgba(26, 29, 32, 0.95);
  backdrop-filter: var(--premium-blur);
  border-top: 1px solid var(--premium-border);
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px;
}

.nav-button {
  position: relative;
  background: transparent;
  border: none;
  border-radius: 12px;
  color: var(--premium-accent);
  padding: 12px 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--premium-gradient);
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-button:hover::before,
.nav-button.active::before {
  opacity: 1;
}

.nav-button.active {
  color: var(--premium-primary);
}

.nav-button:hover {
  color: var(--premium-primary);
  transform: translateY(-1px);
}

.nav-icon {
  width: 24px;
  height: 24px;
  fill: currentColor;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
}

.nav-text {
  font-size: 12px;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

/* --- ПРЕМИУМ СЕКЦИИ --- */
.app-section {
  background: rgba(26, 29, 32, 0.8);
  backdrop-filter: var(--premium-blur);
  border: 1px solid var(--premium-border);
  border-radius: 24px;
  margin: 16px;
  padding: 24px;
  box-shadow: var(--premium-shadow-md);
  animation: premium-fade-in 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.app-section h2 {
  color: var(--premium-primary);
  font-size: 24px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 24px;
  letter-spacing: 1px;
  position: relative;
}

.app-section h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--premium-primary), transparent);
  opacity: 0.3;
}

/* --- ПРЕМИУМ КАРТОЧКИ --- */
.cyber-card,
.cyber-border {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--premium-border);
  border-radius: 16px;
  padding: 20px;
  margin: 12px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cyber-card:hover,
.cyber-border:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
  box-shadow: var(--premium-shadow-md);
}

/* --- ПРЕМИУМ ФОРМЫ --- */
.form-input,
.input-group input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--premium-border);
  border-radius: 12px;
  color: var(--premium-primary);
  padding: 12px 16px;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
}

.form-input:focus,
.input-group input:focus {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
  outline: none;
}

/* --- ПРЕМИУМ СТАТИСТИКА --- */
.cyber-stat {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--premium-border);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cyber-stat:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-1px);
}

.cyber-stat-value {
  color: var(--premium-primary);
  font-size: 24px;
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

.cyber-stat-label {
  color: var(--premium-accent);
  font-size: 14px;
  font-weight: 500;
}

/* --- ПРЕМИУМ ИКОНКИ --- */
.nav-icon, .balance-icon, .user-avatar-icon {
  fill: currentColor;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.balance-icon {
  width: 20px;
  height: 20px;
  fill: var(--premium-primary);
}

.user-avatar-icon {
  width: 24px;
  height: 24px;
  fill: var(--premium-primary);
}

/* --- ПРЕМИУМ СТАТУС СООБЩЕНИЕ --- */
.status-message {
  background: var(--premium-gradient);
  border: 1px solid var(--premium-border);
  border-radius: 12px;
  padding: 16px 20px;
  text-align: center;
  color: var(--premium-primary);
  font-weight: 500;
  margin-bottom: 24px;
  animation: premium-glow 3s ease-in-out infinite;
}

/* --- ПРЕМИУМ ПОДСКАЗКИ --- */
.hint {
  color: var(--premium-accent);
  font-size: 14px;
  line-height: 1.5;
  margin: 8px 0;
}

.hint strong {
  color: var(--premium-primary);
  font-weight: 600;
}

/* --- ПРЕМИУМ СЕЛЕКТЫ --- */
select {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--premium-border);
  border-radius: 12px;
  color: var(--premium-primary);
  padding: 12px 16px;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  cursor: pointer;
}

select:focus {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
  outline: none;
}

select option {
  background: var(--premium-darker);
  color: var(--premium-primary);
}

/* --- ПРЕМИУМ ЛЕЙБЛЫ --- */
label {
  color: var(--premium-primary);
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 8px;
  display: block;
  letter-spacing: 0.5px;
}

/* --- ПРЕМИУМ КНОПКА КОПИРОВАНИЯ --- */
.copy-button {
  background: var(--premium-gradient);
  border: 1px solid var(--premium-border);
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-button:hover {
  background: var(--premium-gradient-hover);
  transform: scale(1.05);
}

.copy-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.button-icon {
  width: 16px;
  height: 16px;
  fill: var(--premium-primary);
}

/* --- ПРЕМИУМ ОБЛАСТЬ ССЫЛКИ --- */
.referral-link-area {
  display: flex;
  gap: 8px;
  align-items: center;
}

.referral-link-area input {
  flex: 1;
}

/* --- ПРЕМИУМ ИСТОРИЯ ВЫПЛАТ --- */
.withdrawal-history {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--premium-border);
  border-radius: 12px;
  padding: 16px;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-list {
  color: var(--premium-accent);
  font-style: italic;
}

/* --- ПРЕМИУМ СПИСОК РЕФЕРАЛОВ --- */
.referrals-list {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--premium-border);
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
}

/* --- ПРЕМИУМ ЗАГОЛОВКИ --- */
h3 {
  color: var(--premium-primary);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  letter-spacing: 0.5px;
}

/* --- ПРЕМИУМ БЛОКИ --- */
.friends-block,
.earn-block {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--premium-border);
  border-radius: 16px;
  padding: 20px;
  margin: 16px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.friends-block:hover,
.earn-block:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
  box-shadow: var(--premium-shadow-md);
}

/* --- АДАПТИВНОСТЬ --- */
@media (max-width: 768px) {
  .app-section {
    margin: 12px;
    padding: 20px;
    border-radius: 20px;
  }

  .action-button {
    padding: 14px 20px;
    font-size: 15px;
  }

  .nav-button {
    padding: 10px 12px;
  }

  .nav-icon {
    width: 20px;
    height: 20px;
  }

  .nav-text {
    font-size: 11px;
  }

  .friends-block,
  .earn-block {
    padding: 16px;
    margin: 12px 0;
  }

  .referral-link-area {
    flex-direction: column;
    gap: 12px;
  }

  .referral-link-area input {
    width: 100%;
  }
}
