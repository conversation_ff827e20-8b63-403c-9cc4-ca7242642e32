/* ======================================== */
/* REFINED CYBERPUNK DESIGN - Утонченный киберпанк дизайн */
/* ======================================== */

/* --- УТОНЧЕННАЯ КИБЕРПАНК ПАЛИТРА --- */
:root {
  /* Основные неоновые цвета - приглушенные */
  --cyber-blue: #00d4ff;
  --cyber-cyan: #00ffff;
  --cyber-purple: #9d4edd;
  --cyber-pink: #ff006e;
  --cyber-green: #00f5ff;
  --cyber-orange: #ff7b00;
  
  /* Темные основы */
  --cyber-dark: #0a0a0a;
  --cyber-dark-secondary: #1a1a1a;
  --cyber-dark-tertiary: #2a2a2a;
  --cyber-surface: rgba(255, 255, 255, 0.05);
  --cyber-surface-hover: rgba(255, 255, 255, 0.08);
  
  /* Утонченные градиенты */
  --cyber-gradient-main: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(157, 78, 221, 0.1) 100%);
  --cyber-gradient-hover: linear-gradient(135deg, rgba(0, 212, 255, 0.15) 0%, rgba(157, 78, 221, 0.15) 100%);
  --cyber-gradient-border: linear-gradient(135deg, var(--cyber-blue) 0%, var(--cyber-purple) 100%);
  
  /* Тонкие тени и свечение */
  --cyber-glow-subtle: 0 0 10px rgba(0, 212, 255, 0.3);
  --cyber-glow-medium: 0 0 20px rgba(0, 212, 255, 0.4);
  --cyber-glow-strong: 0 0 30px rgba(0, 212, 255, 0.5);
  
  /* Размытие */
  --cyber-blur: blur(20px);
  --cyber-blur-strong: blur(40px);
}

/* --- УТОНЧЕННЫЕ АНИМАЦИИ --- */
@keyframes refined-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes refined-glow {
  0%, 100% { 
    box-shadow: var(--cyber-glow-subtle);
  }
  50% { 
    box-shadow: var(--cyber-glow-medium);
  }
}

@keyframes refined-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
}

@keyframes refined-scan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes refined-fade-in {
  from { 
    opacity: 0; 
    transform: translateY(10px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

/* --- ОСНОВНОЙ КОНТЕЙНЕР --- */
.app-container {
  background:
    url('images/cyberpunk-bg.svg') center/cover no-repeat,
    radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(157, 78, 221, 0.03) 0%, transparent 50%),
    linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
  background-attachment: fixed;
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

/* Тонкая киберпанк сетка */
.app-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(0, 212, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.05) 1px, transparent 1px);
  background-size: 80px 80px;
  pointer-events: none;
  z-index: -1;
  animation: refined-pulse 6s ease-in-out infinite;
}

/* Движущиеся энергетические линии */
.app-container::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.08), transparent);
  opacity: 0.6;
  pointer-events: none;
  z-index: -1;
  animation: refined-scan 12s linear infinite;
}

/* --- УТОНЧЕННАЯ ШАПКА --- */
.app-header {
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: var(--cyber-blur);
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  position: relative;
}

.app-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: var(--cyber-gradient-border);
  opacity: 0.6;
  animation: refined-pulse 4s ease-in-out infinite;
}

/* Пользовательская информация */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--cyber-gradient-main);
  border: 1px solid rgba(0, 212, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: refined-glow 4s ease-in-out infinite;
}

.user-avatar:hover {
  background: var(--cyber-gradient-hover);
  transform: scale(1.05);
  box-shadow: var(--cyber-glow-medium);
}

.user-name {
  color: var(--cyber-blue);
  font-weight: 500;
  font-size: 15px;
  letter-spacing: 0.5px;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

/* Информация о балансе */
.balance-info {
  background: var(--cyber-gradient-main);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 18px;
  padding: 6px 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  backdrop-filter: var(--cyber-blur);
}

.balance-info:hover {
  background: var(--cyber-gradient-hover);
  transform: translateY(-1px);
  box-shadow: var(--cyber-glow-medium);
  border-color: rgba(0, 212, 255, 0.5);
}

.balance-amount {
  color: var(--cyber-green);
  font-weight: 600;
  font-size: 15px;
  text-shadow: 0 0 8px rgba(0, 245, 255, 0.4);
}

.balance-currency {
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
}

/* --- УТОНЧЕННЫЕ КНОПКИ --- */
.action-button {
  position: relative;
  background: var(--cyber-gradient-main);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 12px;
  color: var(--cyber-blue);
  font-weight: 500;
  font-size: 15px;
  letter-spacing: 0.5px;
  padding: 14px 20px;
  margin: 6px 0;
  width: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  backdrop-filter: var(--cyber-blur);
  cursor: pointer;
  outline: none;
  text-shadow: 0 0 8px rgba(0, 212, 255, 0.3);
}

/* Тонкий эффект сканирования */
.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.15), transparent);
  transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-button:hover::before {
  left: 100%;
}

.action-button:hover {
  background: var(--cyber-gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--cyber-glow-medium);
  border-color: rgba(0, 212, 255, 0.5);
  color: var(--cyber-cyan);
  animation: refined-float 2s ease-in-out infinite;
}

.action-button:active {
  transform: translateY(0);
  box-shadow: var(--cyber-glow-subtle);
}

.action-button:disabled {
  background: rgba(255, 255, 255, 0.02);
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  animation: none;
}

/* --- УТОНЧЕННАЯ НАВИГАЦИЯ --- */
.app-nav {
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: var(--cyber-blur);
  border-top: 1px solid rgba(0, 212, 255, 0.2);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
  padding: 6px;
}

.nav-button {
  position: relative;
  background: transparent;
  border: none;
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.6);
  padding: 10px 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--cyber-gradient-main);
  border-radius: 10px;
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-button:hover::before,
.nav-button.active::before {
  opacity: 1;
}

.nav-button.active {
  color: var(--cyber-blue);
  text-shadow: 0 0 8px rgba(0, 212, 255, 0.4);
}

.nav-button:hover {
  color: var(--cyber-cyan);
  transform: translateY(-1px);
}

.nav-icon {
  width: 20px;
  height: 20px;
  stroke: currentColor;
  fill: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
  filter: drop-shadow(0 0 4px currentColor);
}

.nav-text {
  font-size: 11px;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

/* --- УТОНЧЕННЫЕ СЕКЦИИ --- */
.app-section {
  background: rgba(10, 10, 10, 0.8);
  backdrop-filter: var(--cyber-blur);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 16px;
  margin: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  animation: refined-fade-in 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.app-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--cyber-gradient-border);
  opacity: 0.4;
  border-radius: 16px 16px 0 0;
}

.app-section h2 {
  color: var(--cyber-blue);
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 20px;
  letter-spacing: 1px;
  text-shadow: 0 0 12px rgba(0, 212, 255, 0.4);
  position: relative;
}

.app-section h2::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 1px;
  background: var(--cyber-gradient-border);
  opacity: 0.6;
}

/* --- УТОНЧЕННЫЕ ИКОНКИ --- */
.nav-icon, .balance-icon, .user-avatar-icon {
  stroke: currentColor;
  fill: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 0 4px currentColor);
}

.balance-icon {
  width: 18px;
  height: 18px;
  stroke: var(--cyber-green);
}

.user-avatar-icon {
  width: 20px;
  height: 20px;
  stroke: var(--cyber-blue);
}

/* --- УТОНЧЕННЫЕ ФОРМЫ --- */
.form-input,
.input-group input {
  background: var(--cyber-surface);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 10px;
  color: var(--cyber-blue);
  padding: 10px 14px;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  backdrop-filter: var(--cyber-blur);
}

.form-input:focus,
.input-group input:focus {
  background: var(--cyber-surface-hover);
  border-color: rgba(0, 212, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.1);
  outline: none;
  text-shadow: 0 0 6px rgba(0, 212, 255, 0.3);
}

/* --- УТОНЧЕННАЯ СТАТИСТИКА --- */
.cyber-stat {
  background: var(--cyber-surface);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 10px;
  padding: 12px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: var(--cyber-blur);
}

.cyber-stat:hover {
  background: var(--cyber-surface-hover);
  transform: translateY(-1px);
  box-shadow: var(--cyber-glow-subtle);
}

.cyber-stat-value {
  color: var(--cyber-green);
  font-size: 18px;
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
  text-shadow: 0 0 8px rgba(0, 245, 255, 0.4);
}

.cyber-stat-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
}

/* --- УТОНЧЕННЫЕ БЛОКИ --- */
.friends-block,
.earn-block {
  background: var(--cyber-surface);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 12px;
  padding: 16px;
  margin: 12px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: var(--cyber-blur);
}

.friends-block:hover,
.earn-block:hover {
  background: var(--cyber-surface-hover);
  border-color: rgba(0, 212, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: var(--cyber-glow-subtle);
}

/* --- СТАТУС СООБЩЕНИЕ --- */
.status-message {
  background: var(--cyber-gradient-main);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 10px;
  padding: 12px 16px;
  text-align: center;
  color: var(--cyber-blue);
  font-weight: 500;
  margin-bottom: 16px;
  animation: refined-glow 3s ease-in-out infinite;
  backdrop-filter: var(--cyber-blur);
  text-shadow: 0 0 8px rgba(0, 212, 255, 0.3);
}

/* --- АДАПТИВНОСТЬ --- */
@media (max-width: 768px) {
  .app-section {
    margin: 8px;
    padding: 16px;
    border-radius: 14px;
  }
  
  .action-button {
    padding: 12px 16px;
    font-size: 14px;
  }
  
  .nav-button {
    padding: 8px 10px;
  }
  
  .nav-icon {
    width: 18px;
    height: 18px;
  }
  
  .nav-text {
    font-size: 10px;
  }
  
  .user-avatar {
    width: 32px;
    height: 32px;
  }
  
  .balance-info {
    padding: 5px 12px;
  }
}
