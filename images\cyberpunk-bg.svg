<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Градиенты -->
    <linearGradient id="cyberGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00ffff;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#ff00ff;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#00ff00;stop-opacity:0.8" />
    </linearGradient>
    
    <linearGradient id="cyberGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#8a2be2;stop-opacity:0.7" />
      <stop offset="100%" style="stop-color:#ff4500;stop-opacity:0.7" />
    </linearGradient>
    
    <radialGradient id="cyberGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#00ffff;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#00ffff;stop-opacity:0" />
    </radialGradient>
    
    <!-- Паттерны -->
    <pattern id="cyberGrid" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse">
      <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#00ffff" stroke-width="0.5" opacity="0.3"/>
    </pattern>
    
    <pattern id="cyberCircuits" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
      <rect width="100" height="100" fill="none"/>
      <circle cx="20" cy="20" r="2" fill="#ff00ff" opacity="0.6"/>
      <circle cx="80" cy="80" r="2" fill="#00ff00" opacity="0.6"/>
      <line x1="20" y1="20" x2="80" y2="80" stroke="#00ffff" stroke-width="1" opacity="0.4"/>
      <line x1="20" y1="80" x2="80" y2="20" stroke="#ff00ff" stroke-width="1" opacity="0.4"/>
    </pattern>
    
    <!-- Анимации -->
    <animateTransform id="rotate" attributeName="transform" type="rotate" 
                      values="0 960 540;360 960 540" dur="60s" repeatCount="indefinite"/>
    
    <animate id="pulse" attributeName="opacity" values="0.3;0.8;0.3" dur="3s" repeatCount="indefinite"/>
    
    <animate id="glow" attributeName="r" values="100;200;100" dur="4s" repeatCount="indefinite"/>
  </defs>
  
  <!-- Основной фон -->
  <rect width="1920" height="1080" fill="#0a0a0a"/>
  
  <!-- Сетка -->
  <rect width="1920" height="1080" fill="url(#cyberGrid)"/>
  
  <!-- Схемы -->
  <rect width="1920" height="1080" fill="url(#cyberCircuits)" opacity="0.3"/>
  
  <!-- Светящиеся элементы -->
  <g opacity="0.6">
    <circle cx="200" cy="200" r="100" fill="url(#cyberGlow)">
      <animateTransform attributeName="transform" type="rotate" 
                        values="0 200 200;360 200 200" dur="20s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="1720" cy="200" r="80" fill="url(#cyberGlow)" opacity="0.7">
      <animateTransform attributeName="transform" type="rotate" 
                        values="360 1720 200;0 1720 200" dur="25s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="200" cy="880" r="120" fill="url(#cyberGlow)" opacity="0.5">
      <animateTransform attributeName="transform" type="rotate" 
                        values="0 200 880;360 200 880" dur="30s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="1720" cy="880" r="90" fill="url(#cyberGlow)" opacity="0.8">
      <animateTransform attributeName="transform" type="rotate" 
                        values="360 1720 880;0 1720 880" dur="18s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Геометрические фигуры -->
  <g stroke="#00ffff" stroke-width="2" fill="none" opacity="0.4">
    <polygon points="960,100 1060,200 960,300 860,200" >
      <animateTransform attributeName="transform" type="rotate" 
                        values="0 960 200;360 960 200" dur="15s" repeatCount="indefinite"/>
    </polygon>
    
    <polygon points="400,400 500,450 450,550 350,500" stroke="#ff00ff">
      <animateTransform attributeName="transform" type="rotate" 
                        values="360 425 475;0 425 475" dur="12s" repeatCount="indefinite"/>
    </polygon>
    
    <polygon points="1400,600 1500,650 1450,750 1350,700" stroke="#00ff00">
      <animateTransform attributeName="transform" type="rotate" 
                        values="0 1425 675;360 1425 675" dur="18s" repeatCount="indefinite"/>
    </polygon>
  </g>
  
  <!-- Линии энергии -->
  <g stroke-width="3" opacity="0.6">
    <line x1="0" y1="300" x2="1920" y2="300" stroke="url(#cyberGradient1)">
      <animate attributeName="opacity" values="0.2;0.8;0.2" dur="2s" repeatCount="indefinite"/>
    </line>
    
    <line x1="0" y1="600" x2="1920" y2="600" stroke="url(#cyberGradient2)">
      <animate attributeName="opacity" values="0.8;0.2;0.8" dur="3s" repeatCount="indefinite"/>
    </line>
    
    <line x1="0" y1="900" x2="1920" y2="900" stroke="url(#cyberGradient1)">
      <animate attributeName="opacity" values="0.3;0.9;0.3" dur="2.5s" repeatCount="indefinite"/>
    </line>
  </g>
  
  <!-- Центральный элемент -->
  <g transform="translate(960, 540)">
    <circle r="50" fill="none" stroke="#00ffff" stroke-width="2" opacity="0.8">
      <animate attributeName="r" values="50;80;50" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;0.3;0.8" dur="4s" repeatCount="indefinite"/>
    </circle>
    
    <circle r="30" fill="none" stroke="#ff00ff" stroke-width="1" opacity="0.6">
      <animate attributeName="r" values="30;60;30" dur="3s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="rotate" 
                        values="0;360" dur="10s" repeatCount="indefinite"/>
    </circle>
    
    <circle r="10" fill="#00ff00" opacity="0.9">
      <animate attributeName="opacity" values="0.9;0.3;0.9" dur="1.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Дополнительные эффекты -->
  <g opacity="0.3">
    <rect x="100" y="100" width="200" height="2" fill="#00ffff">
      <animate attributeName="width" values="200;400;200" dur="3s" repeatCount="indefinite"/>
    </rect>
    
    <rect x="1500" y="500" width="300" height="2" fill="#ff00ff">
      <animate attributeName="width" values="300;100;300" dur="4s" repeatCount="indefinite"/>
    </rect>
    
    <rect x="300" y="800" width="250" height="2" fill="#00ff00">
      <animate attributeName="width" values="250;450;250" dur="2.5s" repeatCount="indefinite"/>
    </rect>
  </g>
</svg>
