/* ======================================== */
/* ULTIMATE CYBERPUNK EFFECTS - Лучшие киберпанк эффекты */
/* ======================================== */

// Инициализация лучших киберпанк эффектов
function initUltimateCyberEffects() {
  // Добавляем лучшие визуальные эффекты
  addUltimateVisualEffects();
  
  // Создаем лучшие интерактивные эффекты
  addUltimateInteractions();
  
  // Добавляем лучшие анимации
  addUltimateAnimations();
  
  // Создаем лучшие частицы
  createUltimateParticles();
  
  // НЕ ТРОГАЕМ навигацию - она работает в main.js
}

// Лучшие визуальные эффекты
function addUltimateVisualEffects() {
  // Добавляем динамическое свечение к важным элементам
  addDynamicGlow();
  
  // Создаем эффект голограммы
  createHologramEffect();
  
  // Добавляем энергетические волны
  createEnergyWaves();
  
  // Создаем эффект сканирования
  createScanEffect();
}

// Создание лучших плавающих частиц
function createUltimateParticles() {
  const particlesContainer = document.createElement('div');
  particlesContainer.className = 'ultimate-particles';
  particlesContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
  `;
  document.body.appendChild(particlesContainer);
  
  // Создаем 25 лучших частиц
  const colors = ['#00ffff', '#ff00ff', '#00ff00', '#ffff00', '#ff4500'];
  
  for (let i = 0; i < 25; i++) {
    const particle = document.createElement('div');
    const color = colors[i % colors.length];
    const size = 2 + Math.random() * 5;
    
    particle.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      background: ${color};
      border-radius: 50%;
      box-shadow: 
        0 0 15px ${color}, 
        0 0 30px ${color}, 
        0 0 45px ${color};
      animation: premium-float ${4 + Math.random() * 8}s ease-in-out infinite;
      left: ${Math.random() * 100}%;
      top: ${Math.random() * 100}%;
      animation-delay: ${Math.random() * 6}s;
      opacity: 0.8;
    `;
    
    particlesContainer.appendChild(particle);
    
    // Добавляем случайное движение
    setInterval(() => {
      particle.style.left = Math.random() * 100 + '%';
      particle.style.top = Math.random() * 100 + '%';
    }, 10000 + Math.random() * 5000);
  }
}

// Добавление динамического свечения
function addDynamicGlow() {
  // Добавляем динамическое свечение к балансу
  const balanceElements = document.querySelectorAll('.balance-amount');
  balanceElements.forEach(element => {
    const observer = new MutationObserver(() => {
      // Создаем эффект вспышки при изменении баланса
      createFlashEffect(element);
    });
    
    observer.observe(element, { childList: true, characterData: true, subtree: true });
  });
}

// Создание эффекта голограммы
function createHologramEffect() {
  const hologramContainer = document.createElement('div');
  hologramContainer.className = 'hologram-effect';
  hologramContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    background: 
      repeating-linear-gradient(
        0deg,
        transparent,
        transparent 2px,
        rgba(0, 255, 255, 0.02) 2px,
        rgba(0, 255, 255, 0.02) 4px
      );
    animation: vibrant-scan 10s linear infinite;
  `;
  document.body.appendChild(hologramContainer);
}

// Создание энергетических волн
function createEnergyWaves() {
  const wavesContainer = document.createElement('div');
  wavesContainer.className = 'energy-waves';
  wavesContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
  `;
  document.body.appendChild(wavesContainer);
  
  // Создаем случайные энергетические волны
  setInterval(() => {
    if (Math.random() < 0.3) {
      createEnergyWave(wavesContainer);
    }
  }, 5000);
}

// Создание одной энергетической волны
function createEnergyWave(container) {
  const wave = document.createElement('div');
  const colors = ['#00ffff', '#ff00ff', '#00ff00', '#ffff00'];
  const color = colors[Math.floor(Math.random() * colors.length)];
  const isHorizontal = Math.random() > 0.5;
  
  if (isHorizontal) {
    wave.style.cssText = `
      position: absolute;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, transparent, ${color}, transparent);
      left: 0;
      top: ${Math.random() * 100}%;
      animation: energy-wave 4s linear;
      box-shadow: 0 0 20px ${color};
      opacity: 0.6;
    `;
  } else {
    wave.style.cssText = `
      position: absolute;
      width: 2px;
      height: 100%;
      background: linear-gradient(0deg, transparent, ${color}, transparent);
      left: ${Math.random() * 100}%;
      top: 0;
      animation: energy-wave 4s linear;
      box-shadow: 0 0 20px ${color};
      opacity: 0.6;
    `;
  }
  
  container.appendChild(wave);
  
  setTimeout(() => {
    wave.remove();
  }, 4000);
}

// Создание эффекта сканирования
function createScanEffect() {
  const scanContainer = document.createElement('div');
  scanContainer.className = 'scan-effect';
  scanContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
  `;
  document.body.appendChild(scanContainer);
  
  // Создаем сканирующие линии
  setInterval(() => {
    if (Math.random() < 0.2) {
      createScanLine(scanContainer);
    }
  }, 3000);
}

// Создание сканирующей линии
function createScanLine(container) {
  const line = document.createElement('div');
  const colors = ['#00ffff', '#ff00ff', '#00ff00'];
  const color = colors[Math.floor(Math.random() * colors.length)];
  
  line.style.cssText = `
    position: absolute;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, ${color}, transparent);
    left: 0;
    top: ${Math.random() * 100}%;
    animation: scan-line 2s linear;
    box-shadow: 0 0 10px ${color};
    opacity: 0.8;
  `;
  
  container.appendChild(line);
  
  setTimeout(() => {
    line.remove();
  }, 2000);
}

// Лучшие интерактивные эффекты
function addUltimateInteractions() {
  // Лучшие эффекты для кнопок
  const buttons = document.querySelectorAll('.action-button');
  buttons.forEach(button => {
    button.addEventListener('mouseenter', () => {
      createUltimateRipple(button);
      button.style.animation = 'premium-breathe 2s ease-in-out infinite';
    });
    
    button.addEventListener('mouseleave', () => {
      button.style.animation = '';
    });
    
    button.addEventListener('click', () => {
      createUltimateExplosion(button);
      createShockwave(button);
    });
  });
  
  // Лучшие эффекты для карточек
  const cards = document.querySelectorAll('.app-section, .friends-block, .earn-block');
  cards.forEach(card => {
    card.addEventListener('mouseenter', () => {
      createAura(card);
    });
    
    card.addEventListener('mouseleave', () => {
      removeAura(card);
    });
  });
  
  // Лучшие эффекты для иконок
  const icons = document.querySelectorAll('.nav-icon, .balance-icon, .user-avatar-icon');
  icons.forEach(icon => {
    icon.addEventListener('mouseenter', () => {
      icon.style.animation = 'premium-rotate 2s ease-in-out infinite';
      icon.style.filter = 'drop-shadow(0 0 20px currentColor) brightness(1.5)';
    });
    
    icon.addEventListener('mouseleave', () => {
      icon.style.animation = '';
      icon.style.filter = 'drop-shadow(0 0 8px currentColor)';
    });
  });
}

// Создание лучшего волнового эффекта
function createUltimateRipple(element) {
  const ripple = document.createElement('div');
  const colors = ['#00ffff', '#ff00ff', '#00ff00', '#ffff00'];
  const color = colors[Math.floor(Math.random() * colors.length)];
  
  ripple.style.cssText = `
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, ${color}, transparent);
    transform: scale(0);
    animation: ultimate-ripple 1s linear;
    left: 50%;
    top: 50%;
    width: 40px;
    height: 40px;
    margin-left: -20px;
    margin-top: -20px;
    pointer-events: none;
    box-shadow: 0 0 30px ${color};
  `;
  
  element.style.position = 'relative';
  element.appendChild(ripple);
  
  setTimeout(() => {
    ripple.remove();
  }, 1000);
}

// Эффект лучшего взрыва
function createUltimateExplosion(element) {
  const explosion = document.createElement('div');
  explosion.style.cssText = `
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    margin-left: -100px;
    margin-top: -100px;
    background: radial-gradient(
      circle,
      rgba(255, 255, 255, 0.9),
      rgba(0, 255, 255, 0.7),
      rgba(255, 0, 255, 0.5),
      rgba(0, 255, 0, 0.3),
      transparent
    );
    border-radius: 50%;
    animation: ultimate-explosion 1s ease-out;
    pointer-events: none;
    z-index: 10;
  `;
  
  element.style.position = 'relative';
  element.appendChild(explosion);
  
  setTimeout(() => {
    explosion.remove();
  }, 1000);
}

// Создание ударной волны
function createShockwave(element) {
  const shockwave = document.createElement('div');
  shockwave.style.cssText = `
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    margin-left: -30px;
    margin-top: -30px;
    border: 4px solid #00ffff;
    border-radius: 50%;
    animation: ultimate-shockwave 1.2s ease-out;
    pointer-events: none;
    z-index: 9;
  `;
  
  element.style.position = 'relative';
  element.appendChild(shockwave);
  
  setTimeout(() => {
    shockwave.remove();
  }, 1200);
}

// Создание ауры
function createAura(element) {
  const aura = document.createElement('div');
  aura.className = 'ultimate-aura';
  aura.style.cssText = `
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.3), transparent);
    border-radius: 25px;
    animation: premium-pulse 2s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
  `;
  
  element.style.position = 'relative';
  element.appendChild(aura);
}

// Удаление ауры
function removeAura(element) {
  const aura = element.querySelector('.ultimate-aura');
  if (aura) {
    aura.remove();
  }
}

// Создание эффекта вспышки
function createFlashEffect(element) {
  const flash = document.createElement('div');
  flash.style.cssText = `
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.9), transparent);
    border-radius: inherit;
    animation: ultimate-flash 0.6s ease-out;
    pointer-events: none;
    z-index: 10;
  `;
  
  element.style.position = 'relative';
  element.appendChild(flash);
  
  setTimeout(() => {
    flash.remove();
  }, 600);
}

// Лучшие анимации
function addUltimateAnimations() {
  // Плавное появление элементов с лучшими эффектами
  const elements = document.querySelectorAll('.app-header, .app-section, .app-nav');
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry, index) => {
      if (entry.isIntersecting) {
        setTimeout(() => {
          entry.target.style.animation = 'premium-fade-in 1s ease forwards';
          entry.target.style.opacity = '1';
          
          // Добавляем эффект появления
          createAppearanceEffect(entry.target);
        }, index * 300);
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.1 });
  
  elements.forEach(element => {
    element.style.opacity = '0';
    observer.observe(element);
  });
}

// Создание эффекта появления
function createAppearanceEffect(element) {
  const appearance = document.createElement('div');
  appearance.style.cssText = `
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      transparent,
      rgba(0, 255, 255, 0.4),
      transparent
    );
    animation: vibrant-scan 1.2s ease-out;
    pointer-events: none;
    z-index: 10;
  `;
  
  element.style.position = 'relative';
  element.appendChild(appearance);
  
  setTimeout(() => {
    appearance.remove();
  }, 1200);
}

// Лучший курсор (только для десктопа)
function initUltimateCursor() {
  if (window.innerWidth <= 768) return;
  
  const cursor = document.createElement('div');
  cursor.style.cssText = `
    position: fixed;
    width: 14px;
    height: 14px;
    background: radial-gradient(circle, #00ffff, #ff00ff, #00ff00);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    mix-blend-mode: difference;
    box-shadow: 
      0 0 25px #00ffff, 
      0 0 50px #ff00ff,
      0 0 75px #00ff00;
    animation: premium-rotate 5s linear infinite;
  `;
  document.body.appendChild(cursor);
  
  document.addEventListener('mousemove', (e) => {
    cursor.style.left = e.clientX - 7 + 'px';
    cursor.style.top = e.clientY - 7 + 'px';
  });
  
  // Лучшие эффекты при наведении
  const interactiveElements = document.querySelectorAll('button, a, input, [role="button"]');
  interactiveElements.forEach(element => {
    element.addEventListener('mouseenter', () => {
      cursor.style.transform = 'scale(3)';
      cursor.style.background = 'radial-gradient(circle, #ff00ff, #ffff00, #00ffff)';
      cursor.style.boxShadow = '0 0 40px #ff00ff, 0 0 80px #ffff00';
    });
    
    element.addEventListener('mouseleave', () => {
      cursor.style.transform = 'scale(1)';
      cursor.style.background = 'radial-gradient(circle, #00ffff, #ff00ff, #00ff00)';
      cursor.style.boxShadow = '0 0 25px #00ffff, 0 0 50px #ff00ff, 0 0 75px #00ff00';
    });
  });
}

// Добавление лучших CSS анимаций
function addUltimateCSS() {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes ultimate-ripple {
      to {
        transform: scale(5);
        opacity: 0;
      }
    }
    
    @keyframes ultimate-explosion {
      0% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
      100% { transform: translate(-50%, -50%) scale(3); opacity: 0; }
    }
    
    @keyframes ultimate-shockwave {
      0% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
      100% { transform: translate(-50%, -50%) scale(8); opacity: 0; }
    }
    
    @keyframes ultimate-flash {
      0% { opacity: 0; }
      50% { opacity: 1; }
      100% { opacity: 0; }
    }
    
    @keyframes energy-wave {
      0% { transform: translateX(-100%); opacity: 0; }
      50% { opacity: 1; }
      100% { transform: translateX(100%); opacity: 0; }
    }
    
    @keyframes scan-line {
      0% { transform: translateY(-100%); opacity: 0; }
      50% { opacity: 1; }
      100% { transform: translateY(100%); opacity: 0; }
    }
    
    /* Скрытие эффектов на слабых устройствах */
    @media (max-width: 768px) {
      .ultimate-particles,
      .energy-waves,
      .hologram-effect,
      .scan-effect {
        display: none;
      }
    }
    
    /* Уменьшение анимаций на мобильных */
    @media (prefers-reduced-motion: reduce) {
      * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }
  `;
  document.head.appendChild(style);
}

// Инициализация всех лучших эффектов
document.addEventListener('DOMContentLoaded', () => {
  // Небольшая задержка чтобы не конфликтовать с main.js
  setTimeout(() => {
    addUltimateCSS();
    initUltimateCyberEffects();
    
    // Лучший курсор только для десктопа
    if (window.innerWidth > 768) {
      initUltimateCursor();
    }
  }, 500);
});

// Экспорт функций для использования в других скриптах
window.UltimateCyberEffects = {
  createUltimateExplosion,
  createShockwave,
  createAura,
  initUltimateCursor
};
