<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no, user-scalable=no, viewport-fit=cover">
    <title>Applanza App</title> <!-- Обновил название -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="cyberpunk-overlay.css">
    <!-- Скрипты подключаются в конце body -->
</head>
<body>

    <!-- Контейнер приложения -->
    <div class="app-container">

        <!-- Шапка -->
        <header class="app-header">
            <div class="user-info">
                <!-- Аватар пока плейсхолдер, можно заменить на img или div с фоном -->
                <div class="user-avatar cyber-glow-pulse">
                    <svg class="user-avatar-icon"><use href="images/cyberpunk-icons.svg#cyber-user"></use></svg>
                </div>
                <div class="user-name" id="user-name">Загрузка...</div>
            </div>
            <div class="balance-info clickable-balance" id="header-balance-info" title="Перейти к выводу средств">
                <!-- Иконка баланса из спрайта -->
                <svg class="balance-icon"><use href="images/cyberpunk-icons.svg#cyber-coin"></use></svg>
                <span class="balance-amount" id="balance-amount">0</span>
                <span class="balance-currency">монет</span> <!-- Оставим "монет" или можно ₽ -->
            </div>
        </header>

        <!-- Основной контент (Задания) -->
        <main class="app-section active-section cyber-card" id="main-content"> <!-- Добавлен класс active-section -->
            <div id="status-message" class="status-message cyber-glow-pulse">Ожидание инициализации...</div>
            <h2 data-section="tasks">Задания</h2>
            <button id="openLinkButton" class="action-button purple-button cyber-scanner">
                Открыть ссылку
            </button>
            <button id="watchVideoButton" class="action-button blue-button cyber-scanner">
                Смотреть видео
            </button>
            <button id="openAdButton" class="action-button orange-button cyber-scanner">
                Открыть рекламу
            </button>
             <!-- <button id="paid-survey-button" class="action-button secondary-action" disabled>
                 <svg class="button-icon"><use href="images/sprite.svg#icon-money"></use></svg>
                 Пройти опрос
            </button> -->
        </main>

        <!-- Секция "Заработок" (Вывод средств - заглушка) -->
        <section class="app-section earn-section page-hidden cyber-card" id="earn-section"> <!-- Добавлен page-hidden -->
            <h2>Вывод средств</h2>
            <div class="earn-block cyber-border">
                <h3>Ваш баланс</h3>
                <div class="current-balance-display cyber-stat">
                    <svg class="balance-icon"><use href="images/sprite.svg#icon-ruble"></use></svg>
                    <span class="balance-amount cyber-stat-value" id="earn-balance-amount">0</span>
                    <span class="balance-currency cyber-stat-label">монет</span>
                </div>
                <p class="hint">Доступно для вывода: <span id="available-withdrawal" class="cyber-stat-value">0</span> монет.</p>
            </div>
             <div class="earn-block cyber-border">
                <h3>💰 Калькулятор вывода</h3>

                <div class="calculator-header">
                    <p class="calculator-subtitle">Курс: 1 монета = $0.001</p>
                    <div class="balance-display">
                        <span class="balance-label">Ваш баланс:</span>
                        <span class="balance-amount" id="calc-balance">0 монет</span>
                    </div>
                </div>

                <!-- Поле ввода суммы -->
                <div class="amount-input-section">
                    <label for="calc-amount">Сумма для вывода:</label>
                    <div class="input-group">
                        <input type="number" id="calc-amount" placeholder="Введите количество монет" min="0" step="1">
                        <span class="input-suffix">монет</span>
                    </div>
                    <div class="amount-info">
                        <span id="dollar-equivalent">= $0.000</span>
                        <span id="balance-check" class="balance-status">Введите сумму</span>
                    </div>
                </div>

                <!-- Табы валют -->
                <div class="currency-tabs-container">
                    <div class="currency-tabs-header">
                        <button class="currency-tab active" data-currency="eth">
                            <span class="tab-icon">⭐</span>
                            <span class="tab-name">Ethereum</span>
                            <span class="tab-symbol">ETH</span>
                        </button>
                        <button class="currency-tab" data-currency="btc">
                            <span class="tab-icon">₿</span>
                            <span class="tab-name">Bitcoin</span>
                            <span class="tab-symbol">BTC</span>
                        </button>
                        <button class="currency-tab" data-currency="usdttrc20">
                            <span class="tab-icon">💲</span>
                            <span class="tab-name">USDT</span>
                            <span class="tab-symbol">TRC20</span>
                        </button>
                        <button class="currency-tab" data-currency="trx">
                            <span class="tab-icon">🔺</span>
                            <span class="tab-name">TRON</span>
                            <span class="tab-symbol">TRX</span>
                        </button>
                    </div>

                    <!-- Контент выбранной валюты -->
                    <div class="currency-content">
                        <div class="currency-info-card" id="currency-info">
                            <div class="currency-main-info">
                                <div class="currency-title">
                                    <span class="currency-icon">⭐</span>
                                    <span class="currency-full-name">Ethereum (ETH)</span>
                                    <span class="currency-badge status-best">Лучший выбор</span>
                                </div>
                                <div class="currency-requirements">
                                    <div class="requirement-item">
                                        <span class="requirement-label">Минимум:</span>
                                        <span class="requirement-value">1,000 монет ($1.00)</span>
                                    </div>
                                    <div class="requirement-item">
                                        <span class="requirement-label">Сетевая комиссия:</span>
                                        <span class="requirement-value fee-amount">$0.53</span>
                                    </div>
                                </div>
                            </div>

                            <div class="calculation-results" id="calculation-results">
                                <div class="calculation-row">
                                    <span class="calc-label">Сумма к выводу:</span>
                                    <span class="calc-value" id="withdrawal-amount-display">-</span>
                                </div>
                                <div class="calculation-row">
                                    <span class="calc-label">Комиссия сети:</span>
                                    <span class="calc-value fee-value" id="fee-amount-display">-</span>
                                </div>
                                <div class="calculation-row total-row">
                                    <span class="calc-label">Вы получите:</span>
                                    <span class="calc-value total-value" id="final-amount-display">-</span>
                                </div>
                                <div class="efficiency-row">
                                    <span class="efficiency-label">Эффективность:</span>
                                    <span class="efficiency-value" id="efficiency-display">-</span>
                                </div>
                            </div>

                            <div class="action-status-card" id="action-status">
                                <div class="status-icon">💡</div>
                                <div class="status-text">Введите сумму для расчета</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

             <div class="earn-block">
                <h3>Заявка на вывод</h3>
                <p class="hint">Выберите валюту из калькулятора выше и укажите адрес кошелька.</p>
                 <div class="withdrawal-form">
                     <label for="crypto-currency">Выбранная криптовалюта:</label>
                     <select id="crypto-currency" class="crypto-select">
                         <option value="usdttrc20">USDT (TRC20)</option>
                         <option value="btc">Bitcoin (BTC)</option>
                         <option value="eth">Ethereum (ETH)</option>
                         <option value="trx">TRON (TRX)</option>
                     </select>

                     <label for="withdrawal-amount">Сумма для вывода (монеты):</label>
                     <input type="number" id="withdrawal-amount" placeholder="Введите сумму" min="0" step="1" readonly>

                     <label for="crypto-amount">Сумма к получению:</label>
                     <input type="text" id="crypto-amount" class="crypto-amount-field" placeholder="Будет рассчитано автоматически" readonly>

                     <label for="withdrawal-address">Адрес кошелька:</label>
                     <input type="text" id="withdrawal-address" placeholder="Введите адрес кошелька">

                     <button id="request-withdrawal-button" class="action-button primary-action" disabled>
                         Запросить вывод
                     </button>
                 </div>
                 <p class="hint error-message" id="withdrawal-error" style="display: none;"></p>
                 <p class="hint"><strong>Важно:</strong> Убедитесь, что адрес кошелька указан корректно. Средства будут отправлены на указанный адрес и не могут быть возвращены в случае ошибки.</p>
            </div>
            <div class="earn-block">
                <h3>История выплат</h3>
                <div id="withdrawal-history" class="withdrawal-history">
                    <div class="placeholder-list">Загрузка истории выплат...</div>
                </div>
            </div>
        </section>

        <!-- Секция "Друзья" -->
        <section class="app-section friends-section page-hidden cyber-card" id="friends-section"> <!-- Добавлен page-hidden -->
            <h2>Друзья и Приглашения</h2>
            <div class="friends-block cyber-border">
                <h3>Поделиться приложением</h3>
                <p>Расскажи друзьям об этом приложении!</p>
                <button id="share-app-button" class="action-button secondary-action cyber-scanner">
                    Поделиться
                </button>
            </div>
            <div class="friends-block cyber-border">
                <h3>Пригласить друга (Ваша ссылка)</h3>
                <p>Поделитесь ссылкой. Вы будете получать 10% от заработка друзей, пришедших по ней!</p>
                <div class="referral-link-area">
                    <input type="text" id="referral-link-input" class="form-input" value="Генерация ссылки..." readonly>
                    <button id="copy-referral-button" class="copy-button cyber-scanner" title="Копировать" disabled>
                        <svg class="button-icon small-icon"><use href="images/sprite.svg#icon-link"></use></svg> <!-- Маленькая иконка копирования -->
                    </button>
                </div>
            </div>
            <div class="friends-block cyber-border">
                <h3>Статистика рефералов</h3>
                <div class="referral-stats">
                    <div class="stat-item cyber-stat">
                        <div class="stat-label cyber-stat-label">Всего рефералов:</div>
                        <div class="stat-value cyber-stat-value" id="referrals-count">0</div>
                    </div>
                    <div class="stat-item cyber-stat">
                        <div class="stat-label cyber-stat-label">Заработано на рефералах:</div>
                        <div class="stat-value cyber-stat-value" id="referral-earnings">0</div>
                    </div>
                </div>
                <div id="referrals-list" class="referrals-list">
                    <p class="hint">У вас пока нет рефералов. Пригласите друзей!</p>
                </div>
                <button id="refresh-stats-button" class="action-button secondary-action">
                    Обновить статистику
                </button>
            </div>
            <div class="friends-block">
                <h3>Подписки</h3>
                <div id="subscriptions-list" class="referrals-list">
                    <p class="hint">Загрузка информации...</p>
                </div>
            </div>
        </section>

        <!-- Нижняя навигация (3 кнопки с SVG) -->
        <nav class="app-nav">
            <button class="nav-button active" id="nav-home">
                <svg class="nav-icon"><use href="images/cyberpunk-icons.svg#cyber-home"></use></svg>
                <span class="nav-text" data-section="tasks">Главная</span>
            </button>
            <button class="nav-button" id="nav-earn">
                <svg class="nav-icon"><use href="images/cyberpunk-icons.svg#cyber-dollar"></use></svg>
                <span class="nav-text" data-section="earnings">Заработок</span>
            </button>
            <button class="nav-button" id="nav-friends">
                <svg class="nav-icon"><use href="images/cyberpunk-icons.svg#cyber-friends"></use></svg>
                <span class="nav-text" data-section="referrals">Друзья</span>
            </button>
        </nav>

    </div> <!-- /app-container -->

    <!-- Подключение скриптов в конце -->
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <script src="https://richinfo.co/richpartners/telegram/js/tg-ob.js"></script>
    <script src="js/localization.js"></script>
    <script src="js/cyberpunk-effects.js"></script>
    <script src="main.js"></script>

</body>
</html>