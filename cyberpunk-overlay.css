/* ======================================== */
/* CYBERPUNK OVERLAY STYLES - Крутой киберпанк дизайн */
/* ======================================== */

/* --- КИБЕРПАНК ЦВЕТОВАЯ ПАЛИТРА --- */
:root {
  /* Неоновые цвета */
  --cyber-neon-blue: #00ffff;
  --cyber-neon-pink: #ff00ff;
  --cyber-neon-green: #00ff00;
  --cyber-neon-purple: #8a2be2;
  --cyber-neon-orange: #ff4500;
  --cyber-neon-yellow: #ffff00;
  
  /* Темные основы */
  --cyber-dark-bg: #0a0a0a;
  --cyber-dark-secondary: #1a1a1a;
  --cyber-dark-tertiary: #2a2a2a;
  
  /* Градиенты */
  --cyber-gradient-main: linear-gradient(135deg, #00ffff 0%, #ff00ff 50%, #00ff00 100%);
  --cyber-gradient-secondary: linear-gradient(45deg, #8a2be2 0%, #ff4500 100%);
  --cyber-gradient-glow: linear-gradient(90deg, transparent, var(--cyber-neon-blue), transparent);
  
  /* Эффекты */
  --cyber-glow-blue: 0 0 20px var(--cyber-neon-blue), 0 0 40px var(--cyber-neon-blue), 0 0 60px var(--cyber-neon-blue);
  --cyber-glow-pink: 0 0 20px var(--cyber-neon-pink), 0 0 40px var(--cyber-neon-pink), 0 0 60px var(--cyber-neon-pink);
  --cyber-glow-green: 0 0 20px var(--cyber-neon-green), 0 0 40px var(--cyber-neon-green), 0 0 60px var(--cyber-neon-green);
}

/* --- КИБЕРПАНК АНИМАЦИИ --- */
@keyframes cyber-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes cyber-glow {
  0%, 100% { 
    box-shadow: var(--cyber-glow-blue);
    border-color: var(--cyber-neon-blue);
  }
  33% { 
    box-shadow: var(--cyber-glow-pink);
    border-color: var(--cyber-neon-pink);
  }
  66% { 
    box-shadow: var(--cyber-glow-green);
    border-color: var(--cyber-neon-green);
  }
}

@keyframes cyber-scan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes cyber-glitch {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
}

@keyframes cyber-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* --- ОСНОВНОЙ КОНТЕЙНЕР ПРИЛОЖЕНИЯ --- */
.app-container {
  position: relative;
  background: var(--cyber-dark-bg);
  background-image: url('images/cyberpunk-bg.svg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  overflow: hidden;
}

/* Киберпанк фоновая сетка */
.app-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  pointer-events: none;
  z-index: -1;
  animation: cyber-pulse 4s ease-in-out infinite;
}

/* Движущиеся линии */
.app-container::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background: var(--cyber-gradient-glow);
  opacity: 0.1;
  pointer-events: none;
  z-index: -1;
  animation: cyber-scan 8s linear infinite;
}

/* --- ШАПКА ПРИЛОЖЕНИЯ --- */
.app-header {
  background: rgba(10, 10, 10, 0.9);
  border-bottom: 2px solid var(--cyber-neon-blue);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
}

.app-header::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--cyber-gradient-main);
  animation: cyber-pulse 2s ease-in-out infinite;
}

/* Пользовательская информация */
.user-info {
  position: relative;
}

.user-avatar {
  border: 2px solid var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
  animation: cyber-glow 6s ease-in-out infinite;
}

.user-name {
  color: var(--cyber-neon-blue);
  text-shadow: 0 0 10px var(--cyber-neon-blue);
  font-weight: 700;
  animation: cyber-pulse 3s ease-in-out infinite;
}

/* Информация о балансе */
.balance-info {
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid var(--cyber-neon-blue);
  border-radius: 15px;
  padding: 8px 16px;
  box-shadow: var(--cyber-glow-blue);
  transition: all 0.3s ease;
}

.balance-info:hover {
  background: rgba(0, 255, 255, 0.2);
  box-shadow: 0 0 30px var(--cyber-neon-blue);
  transform: scale(1.05);
}

.balance-amount {
  color: var(--cyber-neon-green);
  text-shadow: 0 0 10px var(--cyber-neon-green);
  font-weight: 900;
  font-size: 1.2em;
}

/* --- КНОПКИ ДЕЙСТВИЙ --- */
.action-button {
  position: relative;
  background: rgba(10, 10, 10, 0.8);
  border: 2px solid;
  border-radius: 12px;
  color: white;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  overflow: hidden;
  backdrop-filter: blur(5px);
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.action-button:hover::before {
  left: 100%;
}

/* Фиолетовая кнопка */
.purple-button {
  border-color: var(--cyber-neon-purple);
  box-shadow: 0 0 20px rgba(138, 43, 226, 0.5);
}

.purple-button:hover {
  box-shadow: var(--cyber-glow-blue);
  transform: translateY(-3px);
  animation: cyber-float 2s ease-in-out infinite;
}

/* Синяя кнопка */
.blue-button {
  border-color: var(--cyber-neon-blue);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.blue-button:hover {
  box-shadow: var(--cyber-glow-blue);
  transform: translateY(-3px);
  animation: cyber-float 2s ease-in-out infinite;
}

/* Оранжевая кнопка */
.orange-button {
  border-color: var(--cyber-neon-orange);
  box-shadow: 0 0 20px rgba(255, 69, 0, 0.5);
}

.orange-button:hover {
  box-shadow: 0 0 30px var(--cyber-neon-orange);
  transform: translateY(-3px);
  animation: cyber-float 2s ease-in-out infinite;
}

/* --- НАВИГАЦИЯ --- */
.app-nav {
  background: rgba(10, 10, 10, 0.95);
  border-top: 2px solid var(--cyber-neon-blue);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
  backdrop-filter: blur(15px);
}

.nav-button {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 10px;
  margin: 5px;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--cyber-gradient-main);
  opacity: 0;
  border-radius: 10px;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.nav-button.active::before,
.nav-button:hover::before {
  opacity: 0.2;
}

.nav-button.active {
  color: var(--cyber-neon-blue);
  text-shadow: 0 0 10px var(--cyber-neon-blue);
}

.nav-icon {
  filter: drop-shadow(0 0 5px currentColor);
}

/* --- СЕКЦИИ КОНТЕНТА --- */
.app-section {
  position: relative;
  background: rgba(10, 10, 10, 0.7);
  border-radius: 15px;
  margin: 10px;
  padding: 20px;
  border: 1px solid rgba(0, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.app-section h2 {
  color: var(--cyber-neon-blue);
  text-shadow: 0 0 15px var(--cyber-neon-blue);
  text-align: center;
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 30px;
}

/* --- СТАТУС СООБЩЕНИЯ --- */
.status-message {
  background: rgba(0, 255, 0, 0.1);
  border: 1px solid var(--cyber-neon-green);
  border-radius: 10px;
  color: var(--cyber-neon-green);
  text-shadow: 0 0 10px var(--cyber-neon-green);
  padding: 15px;
  text-align: center;
  margin-bottom: 20px;
  animation: cyber-pulse 2s ease-in-out infinite;
}

/* --- КАЛЬКУЛЯТОР ВЫВОДА --- */
.withdrawal-calculator {
  background: rgba(10, 10, 10, 0.9);
  border: 2px solid var(--cyber-neon-purple);
  border-radius: 15px;
  box-shadow: 0 0 30px rgba(138, 43, 226, 0.3);
}

.calculator-header {
  border-bottom: 1px solid var(--cyber-neon-purple);
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.calculator-subtitle {
  color: var(--cyber-neon-yellow);
  text-shadow: 0 0 10px var(--cyber-neon-yellow);
}

/* --- ПОЛЯ ВВОДА --- */
.input-group input {
  background: rgba(10, 10, 10, 0.8);
  border: 2px solid var(--cyber-neon-blue);
  border-radius: 10px;
  color: var(--cyber-neon-blue);
  padding: 15px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.input-group input:focus {
  box-shadow: var(--cyber-glow-blue);
  background: rgba(0, 255, 255, 0.1);
  outline: none;
}

.input-group input::placeholder {
  color: rgba(0, 255, 255, 0.5);
}

/* --- ВКЛАДКИ ВАЛЮТ --- */
.currency-tab {
  background: rgba(10, 10, 10, 0.8);
  border: 1px solid var(--cyber-neon-blue);
  border-radius: 10px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.currency-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--cyber-gradient-main);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.currency-tab.active::before,
.currency-tab:hover::before {
  opacity: 0.2;
}

.currency-tab.active {
  border-color: var(--cyber-neon-green);
  box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
}

/* --- ЭФФЕКТЫ ДЛЯ ТЕКСТА --- */
h1, h2, h3 {
  position: relative;
}

h1::after, h2::after, h3::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 2px;
  background: var(--cyber-gradient-main);
  animation: cyber-pulse 2s ease-in-out infinite;
}

/* --- ДОПОЛНИТЕЛЬНЫЕ КИБЕРПАНК ЭФФЕКТЫ --- */

/* Глитч эффект для важных элементов */
.cyber-glitch {
  animation: cyber-glitch 0.3s ease-in-out infinite alternate;
}

/* Сканирующая линия */
.cyber-scanner {
  position: relative;
  overflow: hidden;
}

.cyber-scanner::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.6), transparent);
  animation: cyber-scan 3s linear infinite;
}

/* Неоновые границы */
.cyber-border {
  position: relative;
  border: 2px solid transparent;
  background: linear-gradient(var(--cyber-dark-bg), var(--cyber-dark-bg)) padding-box,
              var(--cyber-gradient-main) border-box;
  border-radius: 10px;
}

/* Пульсирующее свечение */
.cyber-glow-pulse {
  animation: cyber-glow 4s ease-in-out infinite;
}

/* Плавающие частицы */
.cyber-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.cyber-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: var(--cyber-neon-blue);
  border-radius: 50%;
  animation: cyber-float 6s ease-in-out infinite;
  box-shadow: 0 0 10px var(--cyber-neon-blue);
}

.cyber-particle:nth-child(2n) {
  background: var(--cyber-neon-pink);
  box-shadow: 0 0 10px var(--cyber-neon-pink);
  animation-delay: -2s;
}

.cyber-particle:nth-child(3n) {
  background: var(--cyber-neon-green);
  box-shadow: 0 0 10px var(--cyber-neon-green);
  animation-delay: -4s;
}

/* --- СПЕЦИАЛЬНЫЕ ЭФФЕКТЫ ДЛЯ КНОПОК --- */
.action-button:disabled {
  background: rgba(50, 50, 50, 0.5);
  border-color: #666;
  color: #999;
  box-shadow: none;
  cursor: not-allowed;
}

.action-button:disabled::before {
  display: none;
}

/* Кнопка в состоянии загрузки */
.action-button.loading {
  position: relative;
  color: transparent;
}

.action-button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid var(--cyber-neon-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* --- УЛУЧШЕНИЯ ДЛЯ ФОРМ --- */
.form-group {
  position: relative;
  margin-bottom: 20px;
}

.form-label {
  color: var(--cyber-neon-blue);
  text-shadow: 0 0 5px var(--cyber-neon-blue);
  font-weight: 600;
  margin-bottom: 8px;
  display: block;
}

.form-input {
  width: 100%;
  background: rgba(10, 10, 10, 0.8);
  border: 2px solid var(--cyber-neon-blue);
  border-radius: 8px;
  padding: 12px 16px;
  color: var(--cyber-neon-blue);
  font-size: 16px;
  transition: all 0.3s ease;
}

.form-input:focus {
  box-shadow: var(--cyber-glow-blue);
  background: rgba(0, 255, 255, 0.05);
  outline: none;
}

/* --- КАРТОЧКИ И ПАНЕЛИ --- */
.cyber-card {
  background: rgba(10, 10, 10, 0.9);
  border: 1px solid var(--cyber-neon-blue);
  border-radius: 15px;
  padding: 20px;
  margin: 15px 0;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.cyber-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--cyber-gradient-main);
  animation: cyber-pulse 3s ease-in-out infinite;
}

/* --- СТАТИСТИКА И СЧЕТЧИКИ --- */
.cyber-stat {
  text-align: center;
  padding: 15px;
  background: rgba(0, 255, 255, 0.05);
  border: 1px solid var(--cyber-neon-blue);
  border-radius: 10px;
  margin: 10px 0;
}

.cyber-stat-value {
  font-size: 2em;
  font-weight: 900;
  color: var(--cyber-neon-green);
  text-shadow: 0 0 15px var(--cyber-neon-green);
  display: block;
  animation: cyber-pulse 2s ease-in-out infinite;
}

.cyber-stat-label {
  color: var(--cyber-neon-blue);
  text-shadow: 0 0 5px var(--cyber-neon-blue);
  font-size: 0.9em;
  margin-top: 5px;
}

/* --- АДАПТИВНОСТЬ --- */
@media (max-width: 768px) {
  .cyber-particle {
    display: none; /* Отключаем частицы на мобильных для производительности */
  }

  .app-container::before {
    background-size: 30px 30px; /* Меньшая сетка на мобильных */
  }

  .action-button {
    font-size: 14px;
    padding: 12px 20px;
  }

  .cyber-stat-value {
    font-size: 1.5em;
  }
}

/* --- ДОПОЛНИТЕЛЬНЫЕ УЛУЧШЕНИЯ --- */

/* Стили для SVG иконок */
.nav-icon, .balance-icon, .user-avatar-icon {
  filter: drop-shadow(0 0 5px currentColor);
  transition: all 0.3s ease;
}

.nav-icon:hover, .balance-icon:hover, .user-avatar-icon:hover {
  filter: drop-shadow(0 0 10px currentColor);
  transform: scale(1.1);
}

/* Улучшенные стили для аватара */
.user-avatar-icon {
  width: 100%;
  height: 100%;
  fill: var(--cyber-neon-blue);
}

/* Улучшенные стили для иконки баланса */
.balance-icon {
  width: 24px;
  height: 24px;
  fill: var(--cyber-neon-green);
  margin-right: 8px;
}

/* Стили для навигационных иконок */
.nav-icon {
  width: 24px;
  height: 24px;
  fill: var(--cyber-neon-blue);
}

.nav-button.active .nav-icon {
  fill: var(--cyber-neon-green);
}

/* Эффект печатающегося текста */
.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--cyber-neon-blue);
  white-space: nowrap;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--cyber-neon-blue); }
}

/* Эффект сканирующей линии для кнопок */
.action-button.scanning::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.8), transparent);
  animation: scan-button 2s linear infinite;
}

@keyframes scan-button {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Эффект голограммы для текста */
.hologram-text {
  background: linear-gradient(45deg, var(--cyber-neon-blue), var(--cyber-neon-pink), var(--cyber-neon-green));
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: hologram-shift 3s ease-in-out infinite;
}

@keyframes hologram-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Эффект энергетического поля */
.energy-field {
  position: relative;
  overflow: hidden;
}

.energy-field::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, var(--cyber-neon-blue), transparent);
  animation: energy-rotate 4s linear infinite;
  opacity: 0.3;
  z-index: -1;
}

@keyframes energy-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Стили для кнопок с эффектом нажатия */
.action-button:active {
  transform: scale(0.95);
  box-shadow: inset 0 0 20px rgba(0, 255, 255, 0.5);
}

/* Эффект для полей ввода */
.form-input:focus {
  animation: input-glow 2s ease-in-out infinite;
}

@keyframes input-glow {
  0%, 100% { box-shadow: 0 0 20px var(--cyber-neon-blue); }
  50% { box-shadow: 0 0 30px var(--cyber-neon-pink); }
}

/* Стили для загрузочных состояний */
.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60%, 100% { content: '...'; }
}

/* Эффект матричного текста */
.matrix-text {
  font-family: 'Courier New', monospace;
  color: var(--cyber-neon-green);
  text-shadow: 0 0 10px var(--cyber-neon-green);
  animation: matrix-flicker 0.1s infinite alternate;
}

@keyframes matrix-flicker {
  0% { opacity: 1; }
  100% { opacity: 0.8; }
}
