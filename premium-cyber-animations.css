/* ======================================== */
/* PREMIUM CYBER ANIMATIONS - Премиум киберпанк анимации */
/* ======================================== */

/* --- ПРЕМИУМ ПЕРЕМЕННЫЕ --- */
:root {
  /* Премиум тайминги */
  --premium-fast: 0.2s;
  --premium-normal: 0.4s;
  --premium-slow: 0.8s;
  --premium-ultra-slow: 1.2s;
  
  /* Премиум кривые */
  --premium-ease: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --premium-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --premium-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  --premium-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  
  /* Премиум эффекты */
  --premium-glow: 0 0 30px currentColor, 0 0 60px currentColor, 0 0 90px currentColor;
  --premium-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 10px 20px rgba(0, 0, 0, 0.2);
  --premium-blur: blur(20px);
}

/* --- ПРЕМИУМ АНИМАЦИИ --- */

/* Дорогое появление */
@keyframes premium-fade-in {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    filter: blur(5px);
  }
  50% {
    opacity: 0.7;
    transform: translateY(15px) scale(0.98);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

/* Премиум пульсация */
@keyframes premium-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: var(--premium-glow);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    box-shadow: var(--premium-glow), var(--premium-shadow);
    opacity: 0.9;
  }
}

/* Элитное свечение */
@keyframes premium-glow-wave {
  0% {
    box-shadow: 0 0 20px #00ffff, 0 0 40px #00ffff, 0 0 60px #00ffff;
    border-color: #00ffff;
  }
  25% {
    box-shadow: 0 0 25px #ff00ff, 0 0 50px #ff00ff, 0 0 75px #ff00ff;
    border-color: #ff00ff;
  }
  50% {
    box-shadow: 0 0 30px #00ff00, 0 0 60px #00ff00, 0 0 90px #00ff00;
    border-color: #00ff00;
  }
  75% {
    box-shadow: 0 0 25px #ffff00, 0 0 50px #ffff00, 0 0 75px #ffff00;
    border-color: #ffff00;
  }
  100% {
    box-shadow: 0 0 20px #00ffff, 0 0 40px #00ffff, 0 0 60px #00ffff;
    border-color: #00ffff;
  }
}

/* Дорогое плавание */
@keyframes premium-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    box-shadow: var(--premium-shadow);
  }
  25% {
    transform: translateY(-8px) rotate(1deg);
    box-shadow: 0 25px 45px rgba(0, 0, 0, 0.4);
  }
  50% {
    transform: translateY(-12px) rotate(0deg);
    box-shadow: 0 30px 50px rgba(0, 0, 0, 0.5);
  }
  75% {
    transform: translateY(-8px) rotate(-1deg);
    box-shadow: 0 25px 45px rgba(0, 0, 0, 0.4);
  }
}

/* Премиум сканирование */
@keyframes premium-scan {
  0% {
    transform: translateX(-100%) skewX(-15deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) skewX(-15deg);
    opacity: 0;
  }
}

/* Элитная волна */
@keyframes premium-wave {
  0% {
    transform: translateX(-100%) scaleY(1);
  }
  50% {
    transform: translateX(0%) scaleY(1.2);
  }
  100% {
    transform: translateX(100%) scaleY(1);
  }
}

/* Дорогое вращение */
@keyframes premium-rotate {
  0% {
    transform: rotate(0deg) scale(1);
    filter: hue-rotate(0deg);
  }
  25% {
    transform: rotate(90deg) scale(1.1);
    filter: hue-rotate(90deg);
  }
  50% {
    transform: rotate(180deg) scale(1);
    filter: hue-rotate(180deg);
  }
  75% {
    transform: rotate(270deg) scale(1.1);
    filter: hue-rotate(270deg);
  }
  100% {
    transform: rotate(360deg) scale(1);
    filter: hue-rotate(360deg);
  }
}

/* Премиум дыхание */
@keyframes premium-breathe {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    filter: brightness(1) saturate(1);
  }
  50% {
    transform: scale(1.08) rotate(2deg);
    opacity: 0.9;
    filter: brightness(1.2) saturate(1.3);
  }
}

/* Элитное мерцание */
@keyframes premium-shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

/* Дорогая пульсация границ */
@keyframes premium-border-pulse {
  0%, 100% {
    border-width: 2px;
    border-color: #00ffff;
    box-shadow: inset 0 0 20px rgba(0, 255, 255, 0.2);
  }
  50% {
    border-width: 4px;
    border-color: #ff00ff;
    box-shadow: inset 0 0 40px rgba(255, 0, 255, 0.4);
  }
}

/* Премиум загрузка */
@keyframes premium-loading {
  0% {
    transform: rotate(0deg) scale(1);
    border-color: #00ffff transparent transparent transparent;
  }
  25% {
    transform: rotate(90deg) scale(1.1);
    border-color: #ff00ff transparent transparent transparent;
  }
  50% {
    transform: rotate(180deg) scale(1);
    border-color: #00ff00 transparent transparent transparent;
  }
  75% {
    transform: rotate(270deg) scale(1.1);
    border-color: #ffff00 transparent transparent transparent;
  }
  100% {
    transform: rotate(360deg) scale(1);
    border-color: #00ffff transparent transparent transparent;
  }
}

/* --- ПРЕМИУМ КЛАССЫ --- */

/* Дорогое появление */
.premium-entrance {
  animation: premium-fade-in var(--premium-slow) var(--premium-smooth) forwards;
}

/* Элитная пульсация */
.premium-pulse {
  animation: premium-pulse 3s var(--premium-elastic) infinite;
}

/* Премиум свечение */
.premium-glow {
  animation: premium-glow-wave 4s var(--premium-smooth) infinite;
}

/* Дорогое плавание */
.premium-float {
  animation: premium-float 6s var(--premium-ease) infinite;
}

/* Элитное дыхание */
.premium-breathe {
  animation: premium-breathe 4s var(--premium-smooth) infinite;
}

/* Премиум мерцание */
.premium-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  background-size: 200% 100%;
  animation: premium-shimmer 2s var(--premium-smooth) infinite;
}

/* Дорогие границы */
.premium-borders {
  animation: premium-border-pulse 3s var(--premium-smooth) infinite;
}

/* Элитная загрузка */
.premium-loading {
  width: 40px;
  height: 40px;
  border: 4px solid transparent;
  border-radius: 50%;
  animation: premium-loading 2s var(--premium-smooth) infinite;
}

/* --- ПРЕМИУМ ХОВЕР ЭФФЕКТЫ --- */

.premium-hover {
  transition: all var(--premium-normal) var(--premium-elastic);
  position: relative;
  overflow: hidden;
}

.premium-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left var(--premium-slow) var(--premium-smooth);
}

.premium-hover:hover::before {
  left: 100%;
}

.premium-hover:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: var(--premium-glow), var(--premium-shadow);
  filter: brightness(1.2) saturate(1.3);
}

/* Премиум кнопка */
.premium-button {
  position: relative;
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
  border: 2px solid #00ffff;
  border-radius: 15px;
  padding: 16px 32px;
  color: #00ffff;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 2px;
  cursor: pointer;
  overflow: hidden;
  transition: all var(--premium-normal) var(--premium-elastic);
  backdrop-filter: var(--premium-blur);
}

.premium-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--premium-slow) var(--premium-smooth);
}

.premium-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all var(--premium-normal) var(--premium-elastic);
}

.premium-button:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: var(--premium-glow), var(--premium-shadow);
  border-color: #ff00ff;
  color: #ff00ff;
  background: linear-gradient(135deg, rgba(255, 0, 255, 0.2), rgba(0, 255, 255, 0.2));
}

.premium-button:hover::before {
  left: 100%;
}

.premium-button:hover::after {
  width: 300px;
  height: 300px;
}

.premium-button:active {
  transform: translateY(-2px) scale(1.02);
  transition: all var(--premium-fast) var(--premium-smooth);
}

/* Премиум карточка */
.premium-card {
  background: rgba(10, 10, 10, 0.8);
  backdrop-filter: var(--premium-blur);
  border: 2px solid #00ffff;
  border-radius: 20px;
  padding: 24px;
  position: relative;
  overflow: hidden;
  transition: all var(--premium-normal) var(--premium-elastic);
}

.premium-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00, #ffff00);
  border-radius: 20px;
  z-index: -1;
  opacity: 0;
  transition: opacity var(--premium-normal) var(--premium-smooth);
}

.premium-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--premium-shadow);
}

.premium-card:hover::before {
  opacity: 1;
  animation: premium-rotate 3s linear infinite;
}

/* Премиум иконка */
.premium-icon {
  transition: all var(--premium-normal) var(--premium-elastic);
  filter: drop-shadow(0 0 10px currentColor);
}

.premium-icon:hover {
  transform: scale(1.2) rotate(10deg);
  filter: drop-shadow(0 0 20px currentColor) brightness(1.3);
  animation: premium-pulse 1s var(--premium-elastic) infinite;
}

/* Премиум текст */
.premium-text {
  background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: premium-shimmer 3s ease-in-out infinite;
  font-weight: 700;
  text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

/* Премиум навигация */
.premium-nav-item {
  position: relative;
  transition: all var(--premium-normal) var(--premium-elastic);
}

.premium-nav-item::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, #00ffff, #ff00ff);
  transform: translateX(-50%);
  transition: width var(--premium-normal) var(--premium-elastic);
}

.premium-nav-item:hover::before,
.premium-nav-item.active::before {
  width: 100%;
}

.premium-nav-item:hover {
  transform: translateY(-3px);
  color: #ff00ff;
  text-shadow: 0 0 15px #ff00ff;
}

/* --- АДАПТИВНОСТЬ --- */
@media (max-width: 768px) {
  :root {
    --premium-fast: 0.15s;
    --premium-normal: 0.3s;
    --premium-slow: 0.6s;
    --premium-ultra-slow: 1s;
  }
  
  .premium-hover:hover {
    transform: translateY(-4px) scale(1.02);
  }
  
  .premium-button:hover {
    transform: translateY(-3px) scale(1.02);
  }
  
  .premium-card:hover {
    transform: translateY(-5px) scale(1.01);
  }
}

/* Отключение анимаций для пользователей с ограниченными возможностями */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
