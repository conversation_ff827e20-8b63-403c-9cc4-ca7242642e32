/* ======================================== */
/* VIBRANT CYBERPUNK DESIGN - Яркий и сочный киберпанк дизайн */
/* ======================================== */

/* --- ЯРКАЯ КИБЕРПАНК ПАЛИТРА --- */
:root {
  /* Яркие неоновые цвета */
  --cyber-blue: #00ffff;
  --cyber-pink: #ff00ff;
  --cyber-green: #00ff00;
  --cyber-purple: #8a2be2;
  --cyber-orange: #ff4500;
  --cyber-yellow: #ffff00;
  --cyber-red: #ff0040;
  
  /* Темные основы */
  --cyber-dark: #0a0a0a;
  --cyber-dark-secondary: #1a1a1a;
  --cyber-dark-tertiary: #2a2a2a;
  
  /* Яркие градиенты */
  --cyber-gradient-main: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(255, 0, 255, 0.2) 50%, rgba(0, 255, 0, 0.2) 100%);
  --cyber-gradient-hover: linear-gradient(135deg, rgba(0, 255, 255, 0.3) 0%, rgba(255, 0, 255, 0.3) 50%, rgba(0, 255, 0, 0.3) 100%);
  --cyber-gradient-border: linear-gradient(135deg, var(--cyber-blue) 0%, var(--cyber-pink) 50%, var(--cyber-green) 100%);
  
  /* Яркие свечения */
  --cyber-glow-blue: 0 0 20px var(--cyber-blue), 0 0 40px var(--cyber-blue);
  --cyber-glow-pink: 0 0 20px var(--cyber-pink), 0 0 40px var(--cyber-pink);
  --cyber-glow-green: 0 0 20px var(--cyber-green), 0 0 40px var(--cyber-green);
  --cyber-glow-multi: 0 0 20px var(--cyber-blue), 0 0 40px var(--cyber-pink), 0 0 60px var(--cyber-green);
  
  /* Размытие */
  --cyber-blur: blur(15px);
}

/* --- ЯРКИЕ АНИМАЦИИ --- */
@keyframes vibrant-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes vibrant-glow {
  0%, 100% { 
    box-shadow: var(--cyber-glow-blue);
    border-color: var(--cyber-blue);
  }
  33% { 
    box-shadow: var(--cyber-glow-pink);
    border-color: var(--cyber-pink);
  }
  66% { 
    box-shadow: var(--cyber-glow-green);
    border-color: var(--cyber-green);
  }
}

@keyframes vibrant-rainbow {
  0% { filter: hue-rotate(0deg); }
  100% { filter: hue-rotate(360deg); }
}

@keyframes vibrant-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

@keyframes vibrant-scan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes vibrant-fade-in {
  from { 
    opacity: 0; 
    transform: translateY(20px) scale(0.9); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0) scale(1); 
  }
}

/* --- ОСНОВНОЙ КОНТЕЙНЕР С ФОНАМИ --- */
.app-container {
  background: 
    url('images/bg-1.svg') center/cover no-repeat,
    radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.05) 0%, transparent 70%),
    linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
  background-attachment: fixed;
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

/* Яркая киберпанк сетка */
.app-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(0, 255, 255, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.15) 1px, transparent 1px);
  background-size: 50px 50px;
  pointer-events: none;
  z-index: -1;
  animation: vibrant-pulse 4s ease-in-out infinite;
}

/* Яркие движущиеся линии */
.app-container::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.3), rgba(255, 0, 255, 0.3), rgba(0, 255, 0, 0.3), transparent);
  opacity: 0.8;
  pointer-events: none;
  z-index: -1;
  animation: vibrant-scan 8s linear infinite;
}

/* --- ЯРКАЯ ШАПКА --- */
.app-header {
  background: rgba(10, 10, 10, 0.9);
  backdrop-filter: var(--cyber-blur);
  border-bottom: 2px solid var(--cyber-blue);
  box-shadow: var(--cyber-glow-blue);
  position: relative;
}

.app-header::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--cyber-gradient-border);
  animation: vibrant-rainbow 3s linear infinite;
}

/* Пользовательская информация */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--cyber-gradient-main);
  border: 2px solid var(--cyber-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  animation: vibrant-glow 4s ease-in-out infinite;
}

.user-avatar:hover {
  background: var(--cyber-gradient-hover);
  transform: scale(1.1);
  box-shadow: var(--cyber-glow-multi);
}

.user-name {
  color: var(--cyber-blue);
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0.5px;
  text-shadow: 0 0 10px var(--cyber-blue);
  animation: vibrant-pulse 3s ease-in-out infinite;
}

/* Информация о балансе */
.balance-info {
  background: var(--cyber-gradient-main);
  border: 2px solid var(--cyber-green);
  border-radius: 20px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  backdrop-filter: var(--cyber-blur);
  box-shadow: var(--cyber-glow-green);
}

.balance-info:hover {
  background: var(--cyber-gradient-hover);
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--cyber-glow-multi);
  border-color: var(--cyber-pink);
  animation: vibrant-float 2s ease-in-out infinite;
}

.balance-amount {
  color: var(--cyber-green);
  font-weight: 700;
  font-size: 16px;
  text-shadow: 0 0 15px var(--cyber-green);
  animation: vibrant-pulse 2s ease-in-out infinite;
}

.balance-currency {
  color: var(--cyber-blue);
  font-size: 14px;
  text-shadow: 0 0 8px var(--cyber-blue);
}

/* --- ЯРКИЕ КНОПКИ --- */
.action-button {
  position: relative;
  background: var(--cyber-gradient-main);
  border: 2px solid var(--cyber-blue);
  border-radius: 15px;
  color: var(--cyber-blue);
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 1px;
  text-transform: uppercase;
  padding: 16px 24px;
  margin: 10px 0;
  width: 100%;
  transition: all 0.3s ease;
  overflow: hidden;
  backdrop-filter: var(--cyber-blur);
  cursor: pointer;
  outline: none;
  text-shadow: 0 0 10px var(--cyber-blue);
  box-shadow: var(--cyber-glow-blue);
}

/* Яркий эффект сканирования */
.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.action-button:hover::before {
  left: 100%;
}

.action-button:hover {
  background: var(--cyber-gradient-hover);
  transform: translateY(-3px);
  box-shadow: var(--cyber-glow-multi);
  border-color: var(--cyber-pink);
  color: var(--cyber-pink);
  text-shadow: 0 0 15px var(--cyber-pink);
  animation: vibrant-float 2s ease-in-out infinite;
}

.action-button:active {
  transform: translateY(0) scale(0.98);
  box-shadow: var(--cyber-glow-blue);
}

.action-button:disabled {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.4);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  animation: none;
}

/* --- ЯРКАЯ НАВИГАЦИЯ --- */
.app-nav {
  background: rgba(10, 10, 10, 0.9);
  backdrop-filter: var(--cyber-blur);
  border-top: 2px solid var(--cyber-blue);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
  padding: 8px;
}

.app-nav::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--cyber-gradient-border);
  animation: vibrant-rainbow 3s linear infinite;
}

.nav-button {
  position: relative;
  background: transparent;
  border: none;
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.7);
  padding: 12px 16px;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--cyber-gradient-main);
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.nav-button:hover::before,
.nav-button.active::before {
  opacity: 1;
}

.nav-button.active {
  color: var(--cyber-blue);
  text-shadow: 0 0 10px var(--cyber-blue);
  animation: vibrant-glow 4s ease-in-out infinite;
}

.nav-button:hover {
  color: var(--cyber-pink);
  text-shadow: 0 0 10px var(--cyber-pink);
  transform: translateY(-2px);
}

.nav-icon {
  width: 24px;
  height: 24px;
  fill: currentColor;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  filter: drop-shadow(0 0 8px currentColor);
}

.nav-text {
  font-size: 12px;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

/* --- ЯРКИЕ СЕКЦИИ --- */
.app-section {
  background: rgba(10, 10, 10, 0.8);
  backdrop-filter: var(--cyber-blur);
  border: 2px solid var(--cyber-blue);
  border-radius: 20px;
  margin: 16px;
  padding: 24px;
  box-shadow: var(--cyber-glow-blue);
  animation: vibrant-fade-in 0.8s ease;
  position: relative;
}

.app-section::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--cyber-gradient-border);
  border-radius: 20px;
  z-index: -1;
  animation: vibrant-rainbow 6s linear infinite;
}

.app-section h2 {
  color: var(--cyber-blue);
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 24px;
  letter-spacing: 2px;
  text-transform: uppercase;
  text-shadow: 0 0 20px var(--cyber-blue);
  animation: vibrant-glow 4s ease-in-out infinite;
}

.app-section h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--cyber-gradient-border);
  animation: vibrant-rainbow 3s linear infinite;
}

/* --- ЯРКИЕ ИКОНКИ --- */
.nav-icon, .balance-icon, .user-avatar-icon, .button-icon {
  fill: currentColor;
  transition: all 0.3s ease;
  filter: drop-shadow(0 0 8px currentColor);
}

.balance-icon {
  width: 20px;
  height: 20px;
  fill: var(--cyber-green);
  animation: vibrant-pulse 2s ease-in-out infinite;
}

.user-avatar-icon {
  width: 24px;
  height: 24px;
  fill: var(--cyber-blue);
  animation: vibrant-rainbow 4s linear infinite;
}

.button-icon {
  width: 16px;
  height: 16px;
  fill: var(--cyber-blue);
}

/* --- ЯРКИЕ ФОРМЫ --- */
.form-input,
.input-group input {
  background: rgba(0, 255, 255, 0.1);
  border: 2px solid var(--cyber-blue);
  border-radius: 12px;
  color: var(--cyber-blue);
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  width: 100%;
  backdrop-filter: var(--cyber-blur);
  text-shadow: 0 0 8px var(--cyber-blue);
}

.form-input:focus,
.input-group input:focus {
  background: rgba(255, 0, 255, 0.1);
  border-color: var(--cyber-pink);
  box-shadow: var(--cyber-glow-pink);
  outline: none;
  color: var(--cyber-pink);
  text-shadow: 0 0 10px var(--cyber-pink);
}

/* --- ЯРКАЯ СТАТИСТИКА --- */
.cyber-stat {
  background: var(--cyber-gradient-main);
  border: 2px solid var(--cyber-green);
  border-radius: 15px;
  padding: 16px;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: var(--cyber-blur);
  box-shadow: var(--cyber-glow-green);
}

.cyber-stat:hover {
  background: var(--cyber-gradient-hover);
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--cyber-glow-multi);
  border-color: var(--cyber-pink);
  animation: vibrant-float 2s ease-in-out infinite;
}

.cyber-stat-value {
  color: var(--cyber-green);
  font-size: 24px;
  font-weight: 700;
  display: block;
  margin-bottom: 8px;
  text-shadow: 0 0 15px var(--cyber-green);
  animation: vibrant-pulse 2s ease-in-out infinite;
}

.cyber-stat-label {
  color: var(--cyber-blue);
  font-size: 14px;
  font-weight: 600;
  text-shadow: 0 0 8px var(--cyber-blue);
}

/* --- ЯРКИЕ БЛОКИ --- */
.friends-block,
.earn-block {
  background: rgba(0, 255, 255, 0.05);
  border: 2px solid var(--cyber-blue);
  border-radius: 16px;
  padding: 20px;
  margin: 16px 0;
  transition: all 0.3s ease;
  backdrop-filter: var(--cyber-blur);
  box-shadow: var(--cyber-glow-blue);
}

.friends-block:hover,
.earn-block:hover {
  background: rgba(255, 0, 255, 0.05);
  border-color: var(--cyber-pink);
  transform: translateY(-2px);
  box-shadow: var(--cyber-glow-pink);
}

.friends-block h3,
.earn-block h3 {
  color: var(--cyber-blue);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  text-shadow: 0 0 10px var(--cyber-blue);
}

/* --- СТАТУС СООБЩЕНИЕ --- */
.status-message {
  background: var(--cyber-gradient-main);
  border: 2px solid var(--cyber-green);
  border-radius: 12px;
  padding: 16px 20px;
  text-align: center;
  color: var(--cyber-green);
  font-weight: 600;
  margin-bottom: 20px;
  animation: vibrant-glow 3s ease-in-out infinite;
  backdrop-filter: var(--cyber-blur);
  text-shadow: 0 0 15px var(--cyber-green);
  box-shadow: var(--cyber-glow-green);
}

/* --- КНОПКА КОПИРОВАНИЯ --- */
.copy-button {
  background: var(--cyber-gradient-main);
  border: 2px solid var(--cyber-blue);
  border-radius: 10px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--cyber-glow-blue);
}

.copy-button:hover {
  background: var(--cyber-gradient-hover);
  transform: scale(1.1);
  box-shadow: var(--cyber-glow-pink);
  border-color: var(--cyber-pink);
}

.copy-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* --- АДАПТИВНОСТЬ --- */
@media (max-width: 768px) {
  .app-section {
    margin: 12px;
    padding: 20px;
    border-radius: 16px;
  }
  
  .action-button {
    padding: 14px 20px;
    font-size: 15px;
  }
  
  .nav-button {
    padding: 10px 12px;
  }
  
  .nav-icon {
    width: 22px;
    height: 22px;
  }
  
  .nav-text {
    font-size: 11px;
  }
  
  .user-avatar {
    width: 36px;
    height: 36px;
  }
  
  .balance-info {
    padding: 6px 14px;
  }
  
  /* Упрощаем анимации на мобильных */
  .app-container::before,
  .app-container::after {
    animation-duration: 8s;
  }
}
