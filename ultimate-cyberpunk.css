/* ======================================== */
/* ULTIMATE CYBERPUNK DESIGN - Лучший киберпанк дизайн */
/* ======================================== */

/* --- КИБЕРПАНК ПЕРЕМЕННЫЕ --- */
:root {
  /* Яркие неоновые цвета */
  --cyber-blue: #00ffff;
  --cyber-pink: #ff00ff;
  --cyber-green: #00ff00;
  --cyber-purple: #8a2be2;
  --cyber-orange: #ff4500;
  --cyber-yellow: #ffff00;
  --cyber-red: #ff0040;
  
  /* Темные основы */
  --cyber-dark: #0a0a0a;
  --cyber-dark-secondary: #1a1a1a;
  --cyber-dark-tertiary: #2a2a2a;
  
  /* Премиум тайминги */
  --premium-fast: 0.2s;
  --premium-normal: 0.4s;
  --premium-slow: 0.8s;
  --premium-ultra-slow: 1.2s;
  
  /* Премиум кривые */
  --premium-ease: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --premium-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --premium-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  --premium-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  
  /* Яркие градиенты */
  --cyber-gradient-main: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(255, 0, 255, 0.2) 50%, rgba(0, 255, 0, 0.2) 100%);
  --cyber-gradient-hover: linear-gradient(135deg, rgba(0, 255, 255, 0.3) 0%, rgba(255, 0, 255, 0.3) 50%, rgba(0, 255, 0, 0.3) 100%);
  --cyber-gradient-border: linear-gradient(135deg, var(--cyber-blue) 0%, var(--cyber-pink) 50%, var(--cyber-green) 100%);
  
  /* Премиум свечения */
  --cyber-glow-blue: 0 0 20px var(--cyber-blue), 0 0 40px var(--cyber-blue);
  --cyber-glow-pink: 0 0 20px var(--cyber-pink), 0 0 40px var(--cyber-pink);
  --cyber-glow-green: 0 0 20px var(--cyber-green), 0 0 40px var(--cyber-green);
  --cyber-glow-multi: 0 0 20px var(--cyber-blue), 0 0 40px var(--cyber-pink), 0 0 60px var(--cyber-green);
  --premium-glow: 0 0 30px currentColor, 0 0 60px currentColor, 0 0 90px currentColor;
  --premium-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 10px 20px rgba(0, 0, 0, 0.2);
  
  /* Размытие */
  --cyber-blur: blur(15px);
  --premium-blur: blur(20px);
}

/* --- ПРЕМИУМ АНИМАЦИИ --- */
@keyframes premium-fade-in {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    filter: blur(5px);
  }
  50% {
    opacity: 0.7;
    transform: translateY(15px) scale(0.98);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

@keyframes premium-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: var(--premium-glow);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    box-shadow: var(--premium-glow), var(--premium-shadow);
    opacity: 0.9;
  }
}

@keyframes premium-glow-wave {
  0% {
    box-shadow: 0 0 20px #00ffff, 0 0 40px #00ffff, 0 0 60px #00ffff;
    border-color: #00ffff;
  }
  25% {
    box-shadow: 0 0 25px #ff00ff, 0 0 50px #ff00ff, 0 0 75px #ff00ff;
    border-color: #ff00ff;
  }
  50% {
    box-shadow: 0 0 30px #00ff00, 0 0 60px #00ff00, 0 0 90px #00ff00;
    border-color: #00ff00;
  }
  75% {
    box-shadow: 0 0 25px #ffff00, 0 0 50px #ffff00, 0 0 75px #ffff00;
    border-color: #ffff00;
  }
  100% {
    box-shadow: 0 0 20px #00ffff, 0 0 40px #00ffff, 0 0 60px #00ffff;
    border-color: #00ffff;
  }
}

@keyframes premium-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    box-shadow: var(--premium-shadow);
  }
  25% {
    transform: translateY(-8px) rotate(1deg);
    box-shadow: 0 25px 45px rgba(0, 0, 0, 0.4);
  }
  50% {
    transform: translateY(-12px) rotate(0deg);
    box-shadow: 0 30px 50px rgba(0, 0, 0, 0.5);
  }
  75% {
    transform: translateY(-8px) rotate(-1deg);
    box-shadow: 0 25px 45px rgba(0, 0, 0, 0.4);
  }
}

@keyframes premium-breathe {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    filter: brightness(1) saturate(1);
  }
  50% {
    transform: scale(1.08) rotate(2deg);
    opacity: 0.9;
    filter: brightness(1.2) saturate(1.3);
  }
}

@keyframes premium-shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes premium-rotate {
  0% {
    transform: rotate(0deg) scale(1);
    filter: hue-rotate(0deg);
  }
  25% {
    transform: rotate(90deg) scale(1.1);
    filter: hue-rotate(90deg);
  }
  50% {
    transform: rotate(180deg) scale(1);
    filter: hue-rotate(180deg);
  }
  75% {
    transform: rotate(270deg) scale(1.1);
    filter: hue-rotate(270deg);
  }
  100% {
    transform: rotate(360deg) scale(1);
    filter: hue-rotate(360deg);
  }
}

@keyframes vibrant-scan {
  0% {
    transform: translateX(-100%) skewX(-15deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) skewX(-15deg);
    opacity: 0;
  }
}

/* --- ОСНОВНОЙ КОНТЕЙНЕР --- */
.app-container {
  background: 
    url('images/bg-1.svg') center/cover no-repeat,
    radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.05) 0%, transparent 70%),
    linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
  background-attachment: fixed;
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

/* Яркая киберпанк сетка */
.app-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(0, 255, 255, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.15) 1px, transparent 1px);
  background-size: 50px 50px;
  pointer-events: none;
  z-index: -1;
  animation: premium-pulse 4s ease-in-out infinite;
}

/* Яркие движущиеся линии */
.app-container::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.3), rgba(255, 0, 255, 0.3), rgba(0, 255, 0, 0.3), transparent);
  opacity: 0.8;
  pointer-events: none;
  z-index: -1;
  animation: vibrant-scan 8s linear infinite;
}

/* --- ПРЕМИУМ ШАПКА --- */
.app-header {
  background: rgba(10, 10, 10, 0.9);
  backdrop-filter: var(--premium-blur);
  border-bottom: 2px solid var(--cyber-blue);
  box-shadow: var(--cyber-glow-blue);
  position: relative;
  animation: premium-fade-in var(--premium-slow) var(--premium-smooth) forwards;
}

.app-header::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--cyber-gradient-border);
  animation: premium-rotate 3s linear infinite;
}

/* Пользовательская информация */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--cyber-gradient-main);
  border: 2px solid var(--cyber-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--premium-normal) var(--premium-elastic);
  animation: premium-pulse 3s ease-in-out infinite;
}

.user-avatar:hover {
  background: var(--cyber-gradient-hover);
  transform: scale(1.1);
  box-shadow: var(--cyber-glow-multi);
}

.user-name {
  background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: premium-shimmer 3s ease-in-out infinite;
  font-weight: 700;
  font-size: 16px;
  letter-spacing: 0.5px;
}

/* Информация о балансе */
.balance-info {
  background: var(--cyber-gradient-main);
  border: 2px solid var(--cyber-green);
  border-radius: 20px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all var(--premium-normal) var(--premium-elastic);
  cursor: pointer;
  backdrop-filter: var(--premium-blur);
  box-shadow: var(--cyber-glow-green);
  position: relative;
  overflow: hidden;
  animation: premium-glow-wave 4s ease-in-out infinite;
}

.balance-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--premium-slow) var(--premium-smooth);
}

.balance-info:hover::before {
  left: 100%;
}

.balance-info:hover {
  background: var(--cyber-gradient-hover);
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--cyber-glow-multi);
  border-color: var(--cyber-pink);
  animation: premium-float 2s ease-in-out infinite;
}

.balance-amount {
  color: var(--cyber-green);
  font-weight: 700;
  font-size: 16px;
  text-shadow: 0 0 15px var(--cyber-green);
  animation: premium-breathe 2s ease-in-out infinite;
}

.balance-currency {
  color: var(--cyber-blue);
  font-size: 14px;
  text-shadow: 0 0 8px var(--cyber-blue);
}

/* --- ПРЕМИУМ КНОПКИ --- */
.action-button {
  position: relative;
  background: var(--cyber-gradient-main);
  border: 2px solid var(--cyber-blue);
  border-radius: 15px;
  color: var(--cyber-blue);
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 1px;
  text-transform: uppercase;
  padding: 16px 24px;
  margin: 10px 0;
  width: 100%;
  transition: all var(--premium-normal) var(--premium-elastic);
  overflow: hidden;
  backdrop-filter: var(--premium-blur);
  cursor: pointer;
  outline: none;
  text-shadow: 0 0 10px var(--cyber-blue);
  box-shadow: var(--cyber-glow-blue);
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--premium-slow) var(--premium-smooth);
}

.action-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all var(--premium-normal) var(--premium-elastic);
}

.action-button:hover::before {
  left: 100%;
}

.action-button:hover::after {
  width: 300px;
  height: 300px;
}

.action-button:hover {
  background: var(--cyber-gradient-hover);
  transform: translateY(-5px) scale(1.05);
  box-shadow: var(--cyber-glow-multi);
  border-color: var(--cyber-pink);
  color: var(--cyber-pink);
  text-shadow: 0 0 15px var(--cyber-pink);
  animation: premium-float 2s ease-in-out infinite;
}

.action-button:active {
  transform: translateY(-2px) scale(1.02);
  transition: all var(--premium-fast) var(--premium-smooth);
}

.action-button:disabled {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.4);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  animation: none;
}

/* --- ПРЕМИУМ НАВИГАЦИЯ --- */
.app-nav {
  background: rgba(10, 10, 10, 0.9);
  backdrop-filter: var(--premium-blur);
  border-top: 2px solid var(--cyber-blue);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
  padding: 8px;
  position: relative;
  animation: premium-fade-in var(--premium-slow) var(--premium-smooth) forwards;
}

.app-nav::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--cyber-gradient-border);
  animation: premium-rotate 3s linear infinite;
}

.nav-button {
  position: relative;
  background: transparent;
  border: none;
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.7);
  padding: 12px 16px;
  transition: all var(--premium-normal) var(--premium-elastic);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--cyber-gradient-main);
  border-radius: 12px;
  opacity: 0;
  transition: opacity var(--premium-normal) var(--premium-ease);
}

.nav-button::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, #00ffff, #ff00ff);
  transform: translateX(-50%);
  transition: width var(--premium-normal) var(--premium-elastic);
}

.nav-button:hover::before,
.nav-button.active::before {
  opacity: 1;
}

.nav-button:hover::after,
.nav-button.active::after {
  width: 100%;
}

.nav-button.active {
  color: var(--cyber-blue);
  text-shadow: 0 0 10px var(--cyber-blue);
  animation: premium-glow-wave 4s ease-in-out infinite;
}

.nav-button:hover {
  color: var(--cyber-pink);
  text-shadow: 0 0 10px var(--cyber-pink);
  transform: translateY(-3px);
}

.nav-icon {
  width: 24px;
  height: 24px;
  fill: currentColor;
  transition: all var(--premium-normal) var(--premium-elastic);
  position: relative;
  z-index: 1;
  filter: drop-shadow(0 0 8px currentColor);
}

.nav-icon:hover {
  transform: scale(1.2) rotate(10deg);
  filter: drop-shadow(0 0 20px currentColor) brightness(1.3);
  animation: premium-pulse 1s var(--premium-elastic) infinite;
}

.nav-text {
  font-size: 12px;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

/* --- ПРЕМИУМ СЕКЦИИ --- */
.app-section {
  background: rgba(10, 10, 10, 0.8);
  backdrop-filter: var(--premium-blur);
  border: 2px solid var(--cyber-blue);
  border-radius: 20px;
  margin: 16px;
  padding: 24px;
  box-shadow: var(--cyber-glow-blue);
  animation: premium-fade-in 0.8s ease;
  position: relative;
  overflow: hidden;
  transition: all var(--premium-normal) var(--premium-elastic);
}

.app-section::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--cyber-gradient-border);
  border-radius: 20px;
  z-index: -1;
  animation: premium-rotate 6s linear infinite;
}

.app-section:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--premium-shadow);
}

.app-section h2 {
  background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: premium-shimmer 3s ease-in-out infinite;
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 24px;
  letter-spacing: 2px;
  text-transform: uppercase;
  position: relative;
}

.app-section h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--cyber-gradient-border);
  animation: premium-rotate 3s linear infinite;
}

/* --- ПРЕМИУМ ИКОНКИ --- */
.nav-icon, .balance-icon, .user-avatar-icon, .button-icon {
  fill: currentColor;
  transition: all var(--premium-normal) var(--premium-elastic);
  filter: drop-shadow(0 0 8px currentColor);
}

.balance-icon {
  width: 20px;
  height: 20px;
  fill: var(--cyber-green);
  animation: premium-breathe 2s ease-in-out infinite;
}

.user-avatar-icon {
  width: 24px;
  height: 24px;
  fill: var(--cyber-blue);
  animation: premium-rotate 4s linear infinite;
}

.button-icon {
  width: 16px;
  height: 16px;
  fill: var(--cyber-blue);
}

/* --- ПРЕМИУМ СТАТИСТИКА --- */
.cyber-stat {
  background: var(--cyber-gradient-main);
  border: 2px solid var(--cyber-green);
  border-radius: 15px;
  padding: 16px;
  text-align: center;
  transition: all var(--premium-normal) var(--premium-elastic);
  backdrop-filter: var(--premium-blur);
  box-shadow: var(--cyber-glow-green);
  animation: premium-pulse 3s ease-in-out infinite;
}

.cyber-stat:hover {
  background: var(--cyber-gradient-hover);
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--cyber-glow-multi);
  border-color: var(--cyber-pink);
  animation: premium-float 2s ease-in-out infinite;
}

.cyber-stat-value {
  color: var(--cyber-green);
  font-size: 24px;
  font-weight: 700;
  display: block;
  margin-bottom: 8px;
  text-shadow: 0 0 15px var(--cyber-green);
  animation: premium-breathe 2s ease-in-out infinite;
}

.cyber-stat-label {
  color: var(--cyber-blue);
  font-size: 14px;
  font-weight: 600;
  text-shadow: 0 0 8px var(--cyber-blue);
}

/* --- ПРЕМИУМ БЛОКИ --- */
.friends-block,
.earn-block {
  background: rgba(0, 255, 255, 0.05);
  border: 2px solid var(--cyber-blue);
  border-radius: 16px;
  padding: 20px;
  margin: 16px 0;
  transition: all var(--premium-normal) var(--premium-elastic);
  backdrop-filter: var(--premium-blur);
  box-shadow: var(--cyber-glow-blue);
  position: relative;
  overflow: hidden;
  animation: premium-glow-wave 3s ease-in-out infinite;
}

.friends-block::before,
.earn-block::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00, #ffff00);
  border-radius: 16px;
  z-index: -1;
  opacity: 0;
  transition: opacity var(--premium-normal) var(--premium-smooth);
}

.friends-block:hover::before,
.earn-block:hover::before {
  opacity: 1;
  animation: premium-rotate 3s linear infinite;
}

.friends-block:hover,
.earn-block:hover {
  background: rgba(255, 0, 255, 0.05);
  border-color: var(--cyber-pink);
  transform: translateY(-2px);
  box-shadow: var(--cyber-glow-pink);
}

.friends-block h3,
.earn-block h3 {
  background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: premium-shimmer 3s ease-in-out infinite;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

/* --- СТАТУС СООБЩЕНИЕ --- */
.status-message {
  background: var(--cyber-gradient-main);
  border: 2px solid var(--cyber-green);
  border-radius: 12px;
  padding: 16px 20px;
  text-align: center;
  color: var(--cyber-green);
  font-weight: 600;
  margin-bottom: 20px;
  animation: premium-glow-wave 3s ease-in-out infinite;
  backdrop-filter: var(--premium-blur);
  text-shadow: 0 0 15px var(--cyber-green);
  box-shadow: var(--cyber-glow-green);
  background-size: 200% 100%;
}

/* --- АДАПТИВНОСТЬ --- */
@media (max-width: 768px) {
  :root {
    --premium-fast: 0.15s;
    --premium-normal: 0.3s;
    --premium-slow: 0.6s;
    --premium-ultra-slow: 1s;
  }
  
  .app-section {
    margin: 12px;
    padding: 20px;
    border-radius: 16px;
  }
  
  .action-button {
    padding: 14px 20px;
    font-size: 15px;
  }
  
  .action-button:hover {
    transform: translateY(-3px) scale(1.02);
  }
  
  .nav-button {
    padding: 10px 12px;
  }
  
  .nav-icon {
    width: 22px;
    height: 22px;
  }
  
  .nav-text {
    font-size: 11px;
  }
  
  .user-avatar {
    width: 36px;
    height: 36px;
  }
  
  .balance-info {
    padding: 6px 14px;
  }
  
  /* Упрощаем анимации на мобильных */
  .app-container::before,
  .app-container::after {
    animation-duration: 8s;
  }
}

/* Отключение анимаций для пользователей с ограниченными возможностями */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
