/* ======================================== */
/* STYLISH CYBERPUNK DESIGN - Стильный киберпанк дизайн */
/* ======================================== */

/* --- КИБЕРПАНК ПЕРЕМЕННЫЕ --- */
:root {
  /* Стильные киберпанк цвета */
  --cyber-blue: #00d4ff;
  --cyber-pink: #ff0080;
  --cyber-green: #00ff88;
  --cyber-purple: #9d4edd;
  --cyber-orange: #ff6b35;

  /* Темные основы */
  --cyber-dark: #0a0a0a;
  --cyber-dark-secondary: #1a1a1a;
  --cyber-dark-tertiary: #2a2a2a;

  /* Стильные тайминги */
  --cyber-fast: 0.2s;
  --cyber-normal: 0.3s;
  --cyber-slow: 0.5s;

  /* Стильные кривые */
  --cyber-ease: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --cyber-smooth: cubic-bezier(0.4, 0, 0.2, 1);

  /* Стильные градиенты */
  --cyber-gradient: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(255, 0, 128, 0.1) 100%);
  --cyber-gradient-hover: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(255, 0, 128, 0.2) 100%);
  --cyber-border-gradient: linear-gradient(90deg, var(--cyber-blue), var(--cyber-pink));

  /* Стильные свечения */
  --cyber-glow: 0 0 15px currentColor;
  --cyber-glow-soft: 0 0 10px currentColor;
  --cyber-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);

  /* Размытие */
  --cyber-blur: blur(10px);
}

/* --- СТИЛЬНЫЕ АНИМАЦИИ --- */
@keyframes cyber-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes cyber-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes cyber-glow {
  0%, 100% {
    box-shadow: 0 0 10px var(--cyber-blue);
  }
  50% {
    box-shadow: 0 0 20px var(--cyber-blue), 0 0 30px var(--cyber-pink);
  }
}

@keyframes cyber-slide {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* --- ОСНОВНОЙ КОНТЕЙНЕР --- */
.app-container {
  background:
    url('images/bg-1.svg') center/cover no-repeat,
    linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
  background-attachment: fixed;
  position: relative;
  min-height: 100vh;
}

/* Стильная киберпанк сетка */
.app-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.08) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.08) 1px, transparent 1px);
  background-size: 40px 40px;
  pointer-events: none;
  z-index: -1;
  opacity: 0.6;
}

/* --- СТИЛЬНАЯ ШАПКА --- */
.app-header {
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: var(--cyber-blur);
  border-bottom: 1px solid var(--cyber-blue);
  box-shadow: var(--cyber-shadow);
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  animation: cyber-fade-in var(--cyber-slow) var(--cyber-ease);
}

/* Пользовательская информация */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--cyber-gradient);
  border: 1px solid var(--cyber-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--cyber-normal) var(--cyber-ease);
}

.user-avatar:hover {
  background: var(--cyber-gradient-hover);
  transform: scale(1.05);
  box-shadow: var(--cyber-glow-soft);
}

.user-name {
  color: var(--cyber-blue);
  font-weight: 600;
  font-size: 15px;
  letter-spacing: 0.5px;
}

/* Информация о балансе */
.balance-info {
  background: var(--cyber-gradient);
  border: 1px solid var(--cyber-green);
  border-radius: 12px;
  padding: 8px 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all var(--cyber-normal) var(--cyber-ease);
  cursor: pointer;
  backdrop-filter: var(--cyber-blur);
}

.balance-info:hover {
  background: var(--cyber-gradient-hover);
  transform: translateY(-1px);
  box-shadow: var(--cyber-glow-soft);
  border-color: var(--cyber-pink);
}

.balance-amount {
  color: var(--cyber-green);
  font-weight: 700;
  font-size: 15px;
}

.balance-currency {
  color: var(--cyber-blue);
  font-size: 13px;
}

/* --- СТИЛЬНЫЕ КНОПКИ --- */
.action-button {
  background: var(--cyber-gradient);
  border: 1px solid var(--cyber-blue);
  border-radius: 12px;
  color: var(--cyber-blue);
  font-weight: 600;
  font-size: 15px;
  padding: 14px 20px;
  margin: 8px 0;
  width: 100%;
  transition: all var(--cyber-normal) var(--cyber-ease);
  backdrop-filter: var(--cyber-blur);
  cursor: pointer;
  outline: none;
  position: relative;
  overflow: hidden;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
  transition: left var(--cyber-slow) var(--cyber-ease);
}

.action-button:hover::before {
  left: 100%;
}

.action-button:hover {
  background: var(--cyber-gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--cyber-glow);
  border-color: var(--cyber-pink);
  color: var(--cyber-pink);
}

.action-button:active {
  transform: translateY(0);
}

.action-button:disabled {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.4);
  cursor: not-allowed;
  transform: none;
}

/* --- СТИЛЬНАЯ НАВИГАЦИЯ --- */
.app-nav {
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: var(--cyber-blur);
  border-top: 1px solid var(--cyber-blue);
  box-shadow: var(--cyber-shadow);
  padding: 12px 8px;
  animation: cyber-fade-in var(--cyber-slow) var(--cyber-ease);
}

.nav-button {
  background: transparent;
  border: none;
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.6);
  padding: 10px 12px;
  transition: all var(--cyber-normal) var(--cyber-ease);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 1;
  position: relative;
}

.nav-button::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--cyber-border-gradient);
  transform: translateX(-50%);
  transition: width var(--cyber-normal) var(--cyber-ease);
  border-radius: 1px;
}

.nav-button.active::after,
.nav-button:hover::after {
  width: 60%;
}

.nav-button.active {
  color: var(--cyber-blue);
}

.nav-button:hover {
  color: var(--cyber-pink);
  transform: translateY(-1px);
}

.nav-icon {
  width: 22px;
  height: 22px;
  fill: currentColor;
  transition: all var(--cyber-normal) var(--cyber-ease);
}

.nav-text {
  font-size: 11px;
  font-weight: 500;
}

/* --- СТИЛЬНЫЕ СЕКЦИИ --- */
.app-section {
  background: rgba(10, 10, 10, 0.9);
  backdrop-filter: var(--cyber-blur);
  border: 1px solid var(--cyber-blue);
  border-radius: 16px;
  margin: 16px;
  padding: 20px;
  box-shadow: var(--cyber-shadow);
  animation: cyber-fade-in var(--cyber-slow) var(--cyber-ease);
  transition: all var(--cyber-normal) var(--cyber-ease);
}

.app-section:hover {
  transform: translateY(-2px);
  box-shadow: var(--cyber-glow-soft);
  border-color: var(--cyber-pink);
}

.app-section h2 {
  color: var(--cyber-blue);
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 20px;
  letter-spacing: 1px;
  position: relative;
}

.app-section h2::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: var(--cyber-border-gradient);
  border-radius: 1px;
}

/* --- СТИЛЬНЫЕ ИКОНКИ --- */
.nav-icon, .balance-icon, .user-avatar-icon, .button-icon {
  fill: currentColor;
  transition: all var(--cyber-normal) var(--cyber-ease);
}

.balance-icon {
  width: 18px;
  height: 18px;
  fill: var(--cyber-green);
}

.user-avatar-icon {
  width: 20px;
  height: 20px;
  fill: var(--cyber-blue);
}

.button-icon {
  width: 16px;
  height: 16px;
  fill: var(--cyber-blue);
}

/* --- СТИЛЬНАЯ СТАТИСТИКА --- */
.cyber-stat {
  background: var(--cyber-gradient);
  border: 1px solid var(--cyber-green);
  border-radius: 12px;
  padding: 14px;
  text-align: center;
  transition: all var(--cyber-normal) var(--cyber-ease);
  backdrop-filter: var(--cyber-blur);
}

.cyber-stat:hover {
  background: var(--cyber-gradient-hover);
  transform: translateY(-1px);
  box-shadow: var(--cyber-glow-soft);
  border-color: var(--cyber-pink);
}

.cyber-stat-value {
  color: var(--cyber-green);
  font-size: 20px;
  font-weight: 700;
  display: block;
  margin-bottom: 6px;
}

.cyber-stat-label {
  color: var(--cyber-blue);
  font-size: 13px;
  font-weight: 500;
}

/* --- СТИЛЬНЫЕ БЛОКИ --- */
.friends-block,
.earn-block {
  background: var(--cyber-gradient);
  border: 1px solid var(--cyber-blue);
  border-radius: 14px;
  padding: 18px;
  margin: 12px 0;
  transition: all var(--cyber-normal) var(--cyber-ease);
  backdrop-filter: var(--cyber-blur);
}

.friends-block:hover,
.earn-block:hover {
  background: var(--cyber-gradient-hover);
  border-color: var(--cyber-pink);
  transform: translateY(-1px);
  box-shadow: var(--cyber-glow-soft);
}

.friends-block h3,
.earn-block h3 {
  color: var(--cyber-blue);
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 14px;
}

/* --- СТАТУС СООБЩЕНИЕ --- */
.status-message {
  background: var(--cyber-gradient);
  border: 1px solid var(--cyber-green);
  border-radius: 10px;
  padding: 14px 18px;
  text-align: center;
  color: var(--cyber-green);
  font-weight: 500;
  margin-bottom: 16px;
  backdrop-filter: var(--cyber-blur);
  animation: cyber-pulse 3s ease-in-out infinite;
}

/* --- АДАПТИВНОСТЬ --- */
@media (max-width: 768px) {
  .app-header {
    padding: 14px 16px;
  }

  .app-section {
    margin: 12px;
    padding: 16px;
  }

  .action-button {
    padding: 12px 18px;
    font-size: 14px;
  }

  .nav-button {
    padding: 8px 10px;
  }

  .nav-icon {
    width: 20px;
    height: 20px;
  }

  .nav-text {
    font-size: 10px;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
  }

  .balance-info {
    padding: 6px 12px;
  }
}

/* Отключение анимаций для пользователей с ограниченными возможностями */
@media (prefers-reduced-motion: reduce) {
  * {
    animation: none !important;
    transition-duration: 0.01ms !important;
  }
}
